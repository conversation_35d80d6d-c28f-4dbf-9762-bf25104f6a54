<?php
  ini_set('display_errors', '1');
  ini_set('display_startup_errors', '1');
  error_reporting(E_ALL);

  require 'Helper.php';
  $helper = new Helper();
  $conn = $helper->conn();
  $tanggal = date('Y-m-d');

  $query = "SELECT rp.ID, rp.NOMR, rp.NAMAPASIEN, rp.TANGGAL, rp.NOMOR, rp.`STATUS`
  , kap.NOMOR NOMORKARTU, sk.noSuratKontrol SURATKONTROL, sk.ID IDSURKON
    FROM remun_medis.perjanjian rp
    LEFT JOIN pendaftaran.pendaftaran pp ON rp.NOMR=pp.NORM AND rp.TANGGAL=DATE(pp.TANGGAL)
    LEFT JOIN `master`.kartu_asuransi_pasien kap ON rp.NOMR = kap.NORM AND kap.JENIS=2
    LEFT JOIN bpjs.suratkontrol sk ON kap.NOMOR = sk.noKartu 
    WHERE rp.TANGGAL = '$tanggal' #AND rp.`STATUS`!=0
    AND sk.tglRencanaKontrol=DATE(rp.TANGGAL) AND sk.`status` NOT IN ('0','2')
    AND pp.NOMOR IS NULL";
    $data = $conn->query($query);
    if ($data->num_rows > 0) {
        $array = array();
        while($row = $data->fetch_assoc()) {
            $log = "";
            $param = array(
                "noSuratKontrol" => $row['SURATKONTROL'],
                "user" => "1065"
            );
            $response = $helper->send(json_encode($param),'vclaimv2/rencanaKontrol/delete','DELETE');
            $res = json_decode($response);
            echo json_encode($res);
            $idsuratkontrol = $row['ID'];
            $nosuratkontrol = $row['SURATKONTROL'];
            $request = json_encode($param);
            $code = $res->response->metaData->code;
            $uuid = $helper->guidv4();      
            $log .= "REPLACE INTO remun_medis.log_batal_surat_kontrol (uuid, suratkontrol, request, response, code) VALUES ('$uuid','$nosuratkontrol', '$request', '$response', '$code');";
            $log .= "UPDATE bpjs.suratkontrol SET status = 0 WHERE noSuratKontrol='$nosuratkontrol';";
            if ($conn->multi_query($log) === TRUE) {
                do {
                    if ($result = $conn->store_result()) {
                        $result->free(); // Free the result set
                    }
                } while ($conn->more_results() && $conn->next_result());
                echo "New records created successfully";
            } else {
                echo "Error: " . $log . "<br>" . $conn->error;
            }
        }
    } else {
        echo "0 results";
    }
    $conn->close();

  // echo json_encode($array);