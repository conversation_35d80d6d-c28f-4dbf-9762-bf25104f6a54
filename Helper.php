<?php

Class Helper {
  public function send($data, $url = '', $method='POST') {
    $curl = curl_init();
    
    curl_setopt_array($curl, array(
      CURLOPT_URL => "192.168.7.192:8005/api/$url",
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => $method,
      CURLOPT_POSTFIELDS => $data,
      CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
      ),
    ));
    
    $response = curl_exec($curl);
    $status_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $res = array(
      'code' => $status_code,
      'response' => json_decode($response)
    );
    curl_close($curl);
    return json_encode($res);
  }

  public function conn(){
    $servername = "192.168.7.3";
    $username = "bpjs";
    $password = "simpel";
    $dbname = "remun_medis";

    $conn = new mysqli($servername, $username, $password, $dbname);
    // Check connection
    if ($conn->connect_error) {
      die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
  }

  function guidv4($data = null) {
    // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    // Output the 36 character UUID.
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
  }

  function like_match($pattern, $subject)
  {
      $pattern = str_replace('%', '.*', preg_quote($pattern, '/'));
      return (bool) preg_match("/^{$pattern}$/i", $subject);
  }

  
  function getDoctorByKode($data, $kodeDokter) {
    // return $data->response->list;
    foreach ($data->response as $doctor) {
      if ($doctor->kodedokter == $kodeDokter) {
            return $doctor;
        }
    }
    return null; // Return null if doctor not found
  }
}
