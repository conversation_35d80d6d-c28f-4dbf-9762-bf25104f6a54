<?php
    ini_set('display_errors', '1');
    ini_set('display_startup_errors', '1');
    error_reporting(E_ALL);
    date_default_timezone_set("Asia/Jakarta");
    require 'Helper.php';
    $helper = new Helper();
    $conn = $helper->conn();
    $waktu = date('Y-m-d H:i:s');
    $file_path = 'log-surat-kontrol.txt';
    $query = "SELECT kap.NOMOR NOMOR_KARTU,rmp.TANGGAL, mdok.HAFIS, `master`.getNamaLengkapPegawai(mdok.NIP) NAMA_DOKTER
                ,pr.RUANGAN_PENJAMIN POLI_TUJUAN, pb.nmsubspesialis NAMA_POLI, rmp.NAMAPASIEN NAMA_PASIEN
                FROM remun_medis.perjanjian rmp
                    LEFT JOIN master.dokter mdok ON mdok.ID = rmp.ID_DOKTER
                        LEFT JOIN master.pegawai mpeg ON mpeg.NIP = mdok.NIP AND mdok.STATUS != 0
                        LEFT JOIN master.penjamin_ruangan pr ON mpeg.SMF = pr.RUANGAN_RS
                        LEFT JOIN master.poli_bpjs_2 pb ON pr.RUANGAN_PENJAMIN = pb.kdsubspesialis
                        LEFT JOIN remun_medis.jadwal rmj ON rmj.DOKTER = rmp.ID_DOKTER
                        AND rmj.RUANGAN = rmp.ID_RUANGAN
                        AND rmj.TANGGAL = rmp.TANGGAL AND rmj.STATUS != 0
                    LEFT JOIN master.kartu_asuransi_pasien kap ON kap.NORM =rmp.NOMR	
                    LEFT JOIN master.ruangan mr ON rmp.ID_RUANGAN = mr.id
                    LEFT JOIN bpjs.suratkontrol sk ON kap.NOMOR = sk.noKartu AND sk.tglRencanaKontrol=rmp.TANGGAL AND sk.kodeDokter = mdok.HAFIS
                    WHERE mdok.HAFIS!=0
                    -- AND rmp.TANGGAL = DATE(NOW())
                    AND rmp.TANGGAL BETWEEN (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL 1 DAY),' 00:00:00'))
                    AND (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL 1 DAY),' 23:59:59'))
                    AND rmp.STATUS != 0
                    AND rmp.ID_RUANGAN NOT IN ('105020201') AND kap.JENIS=2
                    AND sk.ID IS NULL AND rmp.RENCANA NOT IN(4)
                    AND mr.GEDUNG IS NULL
                    AND rmp.DPJP = 1
                    -- AND JSON_UNQUOTE(JSON_EXTRACT(sk.note, '$.request.noSEP')) = 'null'
                -- AND pr.RUANGAN_PENJAMIN='017'
                -- AND kap.NOMOR='0001221679675'
                -- AND mdok.HAFIS=217550 
                -- LIMIT 10
                ";
    $dataPerjanjian = $conn->query($query);
    // $conn->next_result();
    if ($dataPerjanjian->num_rows > 0) {
        while($row = $dataPerjanjian->fetch_assoc()) {
            $note = array();
            $hafis = $row['HAFIS'];
            $namaDokter = $row['NAMA_DOKTER'];
            $poliTujuan = $row['POLI_TUJUAN'];
            $nomorKartu = $row['NOMOR_KARTU'];
            $tanggal = $row['TANGGAL'];
            $namaPasien = $row['NAMA_PASIEN'];
            $faskes = 0; // FKTP=1, RS=2
            // Get Jadwal Dokter
            $jadwalDokter = $helper->send([],"antrean/ref/jadwaldokter/kodepoli/$poliTujuan/tanggal/$tanggal",'GET');
            $resJadwalDokter = json_decode($jadwalDokter)->response;
            if($resJadwalDokter->metadata->code == 200){
                $dataJadwalDokter = $helper->getDoctorByKode($resJadwalDokter,$hafis);
                if($dataJadwalDokter === null) {
                    $hafis = $resJadwalDokter->response[0]->kodedokter;
                    $namaDokter = $resJadwalDokter->response[0]->namadokter;
                    $poliTujuan = $resJadwalDokter->response[0]->kodesubspesialis;
                    if($row['HAFIS'] !== $hafis){
                        $note['catatan'][] = 'Dokter DPJP di ubah dari '.$row['HAFIS'].'-'.$row['NAMA_DOKTER'].' menjadi '.$hafis.'-'.$namaDokter;
                    }
                } else {
                    $hafis = $dataJadwalDokter->kodedokter;
                    $namaDokter = $dataJadwalDokter->namadokter;
                    $poliTujuan = $dataJadwalDokter->kodesubspesialis;
                }
            }
            $note['nomorkartu'] = $nomorKartu;
            $note['tanggal'] = $tanggal;
            //Get Rujukan
            $rujukan = $helper->send(json_encode(["noKartu" => $nomorKartu]),'vclaimv2/rujukan/rs','GET');
            $resRujukan = json_decode($rujukan)->response;
            
            if($resRujukan->metaData->code != 200){
                $rujukan = $helper->send(json_encode(["noKartu" => $nomorKartu]),'vclaimv2/rujukan/pcare','GET');
                $resRujukan = json_decode($rujukan)->response;
                if($resRujukan->metaData->code != 200){
                    $note['catatan'] = 'Rujukan tidak ada';
                    echo json_encode($note);
                    continue;
                }
                $note['catatan'][] = 'Rujukan PCARE';
                $faskes = 1;
            }else{
                $note['catatan'][] = 'Rujukan RS';
                $faskes = 2;
            }
            
            // echo json_encode($resRujukan);
            // $diagawal = $resRujukan->response->rujukan[0]->diagnosa->kode;
            // $jenisLayanan = $resRujukan->response->rujukan[0]->pelayanan->kode;
            // $ppkRujukan = $resRujukan->response->rujukan[0]->provPerujuk->kode;
            // $noTelp = $resRujukan->response->rujukan[0]->peserta->mr->noTelepon;
            $noRujukan = $resRujukan->response->rujukan[0]->noKunjungan;
            $tglRujukan = $resRujukan->response->rujukan[0]->tglKunjungan;
            echo 'tglRujukan='.$tglRujukan." - ";
            $poliRujukan = $resRujukan->response->rujukan[0]->poliRujukan->kode;
            $tglRujukan = new DateTime($tglRujukan);
            $dateNow = new DateTime($tanggal);
            $masaBerlakuRujukan = $dateNow->diff($tglRujukan)->format("%a");
            echo $masaBerlakuRujukan."<br/>";
            if($masaBerlakuRujukan > 90){
                echo $masaBerlakuRujukan ."<br/>";
            }
            echo $noRujukan;
            $jumlahSEP = $helper->send(json_encode(["jenis" => $faskes,"noRujukan" => $noRujukan]),'vclaimv2/rujukan/jumlahSEP','GET');
            $resJumlahSEP = json_decode($jumlahSEP)->response;
            $note['catatan'][] = $resJumlahSEP->response->jumlahSEP." - ".$poliTujuan." - ".$poliRujukan;
            $noSep=0;
            $noSuratKontrol=null;
            $status=2;
            $jenisKunjungan=2;
            if($resJumlahSEP->response->jumlahSEP >= 0 && ($poliTujuan == $poliRujukan)) {
                $tglMulai = date('Y-m-d', strtotime('- 90 days'));
                $monitoring =  $helper->send(json_encode(['noKartu' => $nomorKartu, 'tglMulai' => $tglMulai, 'tglAkhir' => date("Y-m-d")]),'vclaimv2/monitoring/historyPeserta','GET');
                $resHistoryPerserta = json_decode($monitoring)->response;
                $dataMonitoring=array();$a=0;
                foreach ($resHistoryPerserta->response->histori as $index => $json) {
                    $namaPoli=$row['NAMA_POLI'];
                    if ($json->ppkPelayanan == 'RS KANKER DHARMAIS' && $helper->like_match("$noRujukan%", $json->noRujukan) && $helper->like_match("$namaPoli%", $json->poli)) {
                        while ($a < 1) {
                            $dataMonitoring = $resHistoryPerserta->response->histori[$index];
                            $a++;
                        }
                    }
                }
                // $querySep = "SELECT pj.NOMOR noSep
                //                 FROM pendaftaran.pendaftaran p
                //             LEFT JOIN pendaftaran.tujuan_pasien tp ON p.NOMOR=tp.NOPEN
                //             LEFT JOIN `master`.kartu_asuransi_pasien kap ON p.NORM=kap.NORM AND kap.JENIS=2
                //             LEFT JOIN pendaftaran.penjamin pj ON p.NOMOR=pj.NOPEN
                //             LEFT JOIN `master`.penjamin_ruangan pr ON pr.RUANGAN_RS=tp.SMF
                //             WHERE p.RUJUKAN='$noRujukan' AND pr.RUANGAN_PENJAMIN='$poliTujuan'
                //             ORDER BY p.TANGGAL DESC LIMIT 1";
                // $dataSep = $conn->query($querySep);
                // $dataMonitoring = $dataSep->fetch_object();

                if($resJumlahSEP->response->jumlahSEP == 0 && $faskes!=0){
                    if($faskes == 1) {
                        $jenisKunjungan = 1;
                    }elseif($faskes == 2){
                        $jenisKunjungan = 4;
                    }
                }
                echo $resJumlahSEP->response->jumlahSEP.'-'.$faskes.'-'.$jenisKunjungan;
                
                if($resJumlahSEP->response->jumlahSEP >= 1) {
                    $listRencanaKontrol = $helper->send(json_encode(['noKartu' => $nomorKartu, 'tahun' => date('Y', strtotime($tanggal)), 'bulan' => date('m', strtotime($tanggal))]),'vclaimv2/rencanaKontrol/list','GET');
                    $resListRencanaKontrol = json_decode($listRencanaKontrol)->response;
                    if (isset($resListRencanaKontrol->response->list)) {
                        foreach ($resListRencanaKontrol->response->list as $index => $json) {
                            if ($json->tglRencanaKontrol == $tanggal) {
                                $resListRencanaKontrol = $resListRencanaKontrol->response->list[$index];
                            }
                        }
                    }

                    if (isset($resListRencanaKontrol->noSuratKontrol)) {
                        $noSuratKontrol=$resListRencanaKontrol->noSuratKontrol;
                        $status=1;
                        $jenisKunjungan = 3;
                    } else {
                        $noSep=$dataMonitoring->noSep;
                        $dataRencanaKontrol = array(
                            "noSEP" => $dataMonitoring->noSep,
                            "kodeDokter" => $hafis,
                            "poliKontrol" => $poliTujuan,
                            "tglRencanaKontrol" => $tanggal,
                            "user" => "1065"
                        );
                        $note['request'] = $dataRencanaKontrol;
                        $rencanaKontrol = $helper->send(json_encode($dataRencanaKontrol), 'vclaimv2/rencanaKontrol/insert', 'POST');
                        $resRencanaKontrol = json_decode($rencanaKontrol)->response;
                        $note['response'] = $resRencanaKontrol;
                        $catatan = json_encode($note);
                        if($resRencanaKontrol->metaData->code == 200){
                            // echo $resRencanaKontrol->response->noSuratKontrol."<br/>";
                            $noSuratKontrol=$resRencanaKontrol->response->noSuratKontrol;
                            $status=1;
                            $jenisKunjungan = 3;
                        }
                    }
                }
                file_put_contents($file_path, date('Y-m-d H:i:s').' '. json_encode($note).PHP_EOL, FILE_APPEND);
            }
            $catatan = json_encode($note);
            $pasien = addslashes($namaPasien);
            $noSuratKontrolValue = isset($noSuratKontrol) ? "'$noSuratKontrol'" : "NULL";
            $insertsuratkontrol = "INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, noRujukan, namaDokter, tglRencanaKontrol, namaPasien, jenisKunjungan, note, user, status) VALUES ('$noSep', '$hafis', '$poliTujuan', $noSuratKontrolValue, '$nomorKartu', '$noRujukan', '$namaDokter', '$tanggal', '$pasien', '$jenisKunjungan', '$catatan', '1065', '$status')";
            if ($conn->multi_query($insertsuratkontrol) === TRUE) {
                $note['database'] = 'sukses';
            } else {
                $note['database'] = "Error: " . $insertsuratkontrol . "<br>" . $conn->error;
            }
            echo json_encode($note);
        }
    }
    $conn->close();