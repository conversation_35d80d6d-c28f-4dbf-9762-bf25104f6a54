<?php
  ini_set('display_errors', '1');
  ini_set('display_startup_errors', '1');
  error_reporting(E_ALL);

  require 'Helper.php';
  $helper = new Helper();
  $conn = $helper->conn();
  $tanggal = date('Y-m-d');

  $queryPerjanjian = "SELECT rp.ID, rp.NOMR, rp.NAMAPASIEN, rp.TANGGAL, rp.NOMOR, rp.`STATUS`
  FROM remun_medis.perjanjian rp
  LEFT JOIN pendaftaran.pendaftaran pp ON rp.NOMR=pp.NORM AND rp.TANGGAL=DATE(pp.TANGGAL)
  WHERE rp.TANGGAL = '$tanggal' AND rp.`STATUS`!=0 
  AND pp.NOMOR IS NULL";
  $dataPerjanjian = $conn->query($queryPerjanjian);
  if ($dataPerjanjian->num_rows > 0) {
    $array = array();
    $log = "";
    while($row = $dataPerjanjian->fetch_assoc()) {
      $data = array(
        "kodebooking" => $row['ID'],
        "taskid" => 99,
        "keterangan" => '-'
      );
      $response = $helper->send(json_encode($data),'antrean/updatewaktu');
      $res = json_decode($response);
      $kodebooking = $row['ID'];
      $request = json_encode($data);
      $code = $res->response->metadata->code;
      $uuid = $helper->guidv4();
      
      $log = "REPLACE INTO remun_medis.log_batal_antrean (uuid, id_perjanjian, request, response, code) VALUES ('$uuid',$kodebooking, '$request', '$response', '$code');";
      if ($conn->multi_query($log) === TRUE) {
        echo "New records created successfully";
      } else {
        echo "Error: " . $log . "<br>" . $conn->error;
      }
    }
  } else {
    echo "0 results";
  }
  $conn->close();

  // echo json_encode($array);