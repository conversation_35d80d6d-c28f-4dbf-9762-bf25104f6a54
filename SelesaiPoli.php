<?php
  ini_set('display_errors', '1');
  ini_set('display_startup_errors', '1');
  error_reporting(E_ALL);
  date_default_timezone_set("Asia/Jakarta");
  require 'Helper.php';
  $helper = new Helper();
  $conn = $helper->conn();
  $query = "CALL `scheduler_cppt`()";
  $result = $conn->query($query);
  if ($result === false) {
    die("Query Error: " . $conn->error);
  }
  $conn->next_result();
  if ($result->num_rows > 0) {
    // output data of each row
    $array = array();
    $log = "";
    while($row = $result->fetch_assoc()) {
        // echo json_encode($row);
        $timestampInMilliseconds = $row['TIMESTAMP'];
        $timestampInSeconds = $timestampInMilliseconds / 1000;
        $dateTime = new DateTime("@$timestampInSeconds");
        $dateTime->setTimezone(new DateTimeZone('Asia/Jakarta'));
        $formattedDateTime = $dateTime->format('Y-m-d H:i:s');
        $currentDateTime = date('Y-m-d H:i:s');
        if ($formattedDateTime<$currentDateTime) {
            $data = array(
                "kodebooking" => $row['ID_PERJANJIAN'],
                "taskid" => $row['TASK_ID'],
                "waktu" => $row['TIMESTAMP'],
            );
            echo $row['ID_PERJANJIAN'];
            $response = $helper->send(json_encode($data),'antrean/updatewaktu');
            $res = json_decode($response);
            $kodebooking = $row['ID_PERJANJIAN'];
            $taskid = $row['TASK_ID'];
            $tanggal = $row['TANGGAL'];
            $request = json_encode($data);
            $code = $res->response->metadata->code;
            $uuid = $helper->guidv4();
            $waktu = date('Y-m-d H:i:s');
            
            $log = "REPLACE INTO remun_medis.log_update_antrean (uuid, id_perjanjian, task_id, tanggal, request, response, code, waktu) VALUES ('$uuid',$kodebooking, '$taskid', '$tanggal', '$request', '$response', '$code', '$waktu');";
            if ($conn->multi_query($log) === TRUE) {
              echo " New records created successfully \n";
            } else {
              echo "Error: " . $log . "<br>" . $conn->error;
            }
        }
    }
  } else {
    echo "0 results";
  }
  $conn->close();
  // echo json_encode($array);