SELECT p.ID kodebooking
#	, IF(rx.GEDUNG IS NULL,'<PERSON><PERSON><PERSON>','NON JKN') jenispasien
   #, IF(sk.ID IS NOT NULL,'JK<PERSON>','NON JKN') jenispasien
	#, IF(pes.<PERSON><PERSON><PERSON>u IS NOT NULL OR rx.GEDUNG IS NULL,'JK<PERSON>','NON JKN') jenispasien
	, IF(rx.GEDUNG IS NULL,'JKN','NON JKN') jenispasien
	, mka.NOMOR nomorkartu
	#, kip.NOMOR nik
	, trim(replace(replace(replace(REPLACE(if(pes.nik IS NULL, kip.NOMOR, pes.nik) 
		,' ',''),'/',''),'.',''),',','')) nik
	#, REGEXP_REPLACE(p.NOMOR, '[^0-9]', '') nohp
   , REPLACE(
        REPLACE(
            REPLACE(p.NOMOR, ' ', ''), 
        '-', ''), 
    '+', '') nohp
	#, p.NOMOR nohp
	, IF(sk.ID IS NOT NULL, sk.poliKontrol, IF(md.ID=10,'UMU',pr.RUANGAN_PENJAMIN)) kodepoli
	, IF(sk.ID IS NOT NULL, mpbs.NAMAPOLI, IF(md.ID=10,'UMUM',mpb.NAMAPOLI)) namapoli
	#, IF(md.ID=10,'UMU',pr.RUANGAN_PENJAMIN) kodepoli
	#, IF(md.ID=10,'UMUM', mpb.NAMAPOLI) namapoli
	#, pr.RUANGAN_PENJAMIN kodepoli, mpb.NAMAPOLI namapoli
	, if(pes.noKartu IS NULL,1,0) pasienbaru
	, p.NOMR norm
	, p.TANGGAL tanggalperiksa
	, IF(sk.ID IS NOT NULL, sk.kodeDokter, md.HAFIS) kodedokter
	, IF(sk.ID IS NOT NULL, sk.namaDokter, master.getNamaLengkapPegawai(md.NIP)) namadokter	
	#, sk.kodeDokter kodedokter, sk.namaDokter namadokter
	, CONCAT(TIME_FORMAT(STR_TO_DATE(j.AWAL,"%H"),"%H:%i"), "-"
	, TIME_FORMAT(STR_TO_DATE(j.AKHIR,"%H"),"%H:%i")) jampraktek
	, sk.jenisKunjungan jeniskunjungan
	#, bk.noRujukan nomorreferensi
	/*, (SELECT sur.noSuratKontrol FROM bpjs.suratkontrol sur
			WHERE sur.tglRencanaKontrol=p.TANGGAL #AND sur.kodeDokter=md.HAFIS
				AND sur.poliKontrol = pr.RUANGAN_PENJAMIN
				AND sur.status=1 AND sur.noKartu=mka.NOMOR
			LIMIT 1) nomorreferensi*/
	, IF(sk.noSuratKontrol IS NULL, sk.noRujukan, sk.noSuratKontrol) nomorreferensi
	, p.NOANTREANBPJS nomorantrean
	, p.NOANTREANBPJS angkaantrean
	, CAST(1000*ROUND(UNIX_TIMESTAMP(
        TIMESTAMPADD(
            MINUTE, 
            5 * (p.NOANTREANBPJS - 1), 
            CONCAT(j.TANGGAL, ' ', LPAD(j.AWAL, 2, '0'), ':00:00')
        )
    ))AS CHAR(50)) estimasidilayani

#	, CAST(1000*ROUND(UNIX_TIMESTAMP(CONCAT(j.TANGGAL, ' ',DATE_ADD(STR_TO_DATE(j.AWAL,"%H:%i:%s")
#		, INTERVAL (IF(p.NOANTREANBPJS=1,1,p.NOANTREANBPJS-1))*15 MINUTE))))AS CHAR(50)) estimasidilayani	

	, j.KUOTA-(SELECT COUNT(px.ID) FROM remun_medis.perjanjian px
		#LEFT JOIN remun_medis.jadwal jx ON jx.DOKTER = px.ID_DOKTER AND jx.RUANGAN = px.ID_RUANGAN AND px.TANGGAL=jx.TANGGAL
		WHERE px.ID_DOKTER=p.ID_DOKTER AND px.ID_RUANGAN=p.ID_RUANGAN AND px.TANGGAL=p.TANGGAL
			#AND jx.RUANGAN NOT LIKE '1050202%' AND jx.RUANGAN NOT LIKE '1050201%' AND jx.RUANGAN NOT LIKE '1050601%' AND px.`STATUS` != 0
		) sisakuotajkn
	, j.KUOTA kuotajkn
	, j.KUOTA-(SELECT COUNT(px.ID) FROM remun_medis.perjanjian px
		#LEFT JOIN remun_medis.jadwal jx ON jx.DOKTER = px.ID_DOKTER AND jx.RUANGAN = px.ID_RUANGAN AND px.TANGGAL=jx.TANGGAL
		WHERE px.ID_DOKTER=p.ID_DOKTER AND px.ID_RUANGAN=p.ID_RUANGAN AND px.TANGGAL=p.TANGGAL
			#AND jx.RUANGAN NOT LIKE '1050202%' AND jx.RUANGAN NOT LIKE '1050201%' AND jx.RUANGAN NOT LIKE '1050601%' AND px.`STATUS` != 0
		) sisakuotanonjkn
	, j.KUOTA kuotanonjkn

  	, NULL keterangan
  	, la.`code`
FROM remun_medis.perjanjian p
	LEFT JOIN remun_medis.log_add_antrean la ON p.ID = la.id_perjanjian
	LEFT JOIN remun_medis.jadwal j ON p.ID_DOKTER=j.DOKTER AND p.ID_RUANGAN=j.RUANGAN AND p.TANGGAL=j.TANGGAL
	LEFT JOIN `master`.kartu_identitas_pasien kip ON p.NOMR=kip.NORM AND kip.JENIS = 1
	LEFT JOIN `master`.dokter md ON p.ID_DOKTER=md.ID
	LEFT JOIN `master`.pegawai mp ON md.NIP = mp.NIP
	LEFT JOIN `master`.penjamin_ruangan pr ON mp.SMF = pr.RUANGAN_RS
	LEFT JOIN `master`.poli_bpjs mpb ON pr.RUANGAN_PENJAMIN=mpb.KODEPOLI
	LEFT JOIN `master`.kartu_asuransi_pasien mka ON p.NOMR = mka.NORM AND mka.JENIS=2
	LEFT JOIN bpjs.suratkontrol sk ON mka.NOMOR = sk.noKartu AND sk.tglRencanaKontrol=p.TANGGAL
	LEFT JOIN master.ruangan rx ON rx.ID = p.ID_RUANGAN
	LEFT JOIN `master`.poli_bpjs mpbs ON sk.poliKontrol=mpbs.KODEPOLI
	#LEFT JOIN pendaftaran.pendaftaran pd ON pd.NORM = p.NOMR AND date(pd.TANGGAL)=p.TANGGAL 
	#LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pd.NOMOR
	LEFT JOIN bpjs.peserta pes ON mka.NOMOR = pes.noKartu
	#LEFT JOIN bpjs.peserta pes ON (pes.norm = p.NOMR OR mka.NOMOR = pes.noKartu)
	#LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM AND kip.JENIS=1
	#LEFT JOIN bpjs.peserta pes ON pes.norm = p.NOMR
	#LEFT JOIN master.pasien ps ON ps.NORM = p.NOMR
	#LEFT JOIN bpjs.kunjungan bk ON bk.noKartu=mka.NOMOR AND DATE(bk.tglSEP) = DATE(pd.TANGGAL)
WHERE p.TANGGAL = '2025-08-25' #AND j.RUANGAN NOT LIKE '1050202%' #AND pes.ketStatusPeserta = 'AKTIF' #AND j.RUANGAN NOT LIKE '1050201%' AND j.RUANGAN NOT LIKE '1050601%' 
AND p.`STATUS` != 0 #AND (la.`code` IS NULL OR la.`code` NOT LIKE '2%') 
AND j.`STATUS` != 0 AND p.NOMR != 0 AND md.HAFIS != 0 AND sk.kodeDokter IS NOT NULL
#AND p.`STATUS` != 0 AND JSON_UNQUOTE(JSON_EXTRACT(la.response, '$.response.metadata.message')) IN('cURL error 28: Operation timed out after 30000 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://apijkn.bpjs-kesehatan.go.id/antreanrs/antrean/add','cURL error 28: Operation timed out after 30001 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://apijkn.bpjs-kesehatan.go.id/antreanrs/antrean/add') AND j.`STATUS` != 0 AND p.NOMR != 0 AND md.HAFIS != 0 AND sk.kodeDokter IS NOT NULL 	  
GROUP BY p.ID
ORDER BY mp.NAMA ASC, p.NOANTREANBPJS ASC