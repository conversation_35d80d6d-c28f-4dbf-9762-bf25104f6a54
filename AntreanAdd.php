<?php
  ini_set('display_errors', '1');
  ini_set('display_startup_errors', '1');
  error_reporting(E_ALL);
  date_default_timezone_set("Asia/Jakarta");
  require 'Helper.php';
  $helper = new Helper();
  $conn = $helper->conn();
  // $data = array(
  //   "kodebooking" => 734925,
  //   "jenispasien" => "JKN",
  //   "nomorkartu" => "0001234567890",
  //   "nik" => "3517131102880003",
  //   "nohp" => "085635228888",
  //   "kodepoli" => "008",
  //   "namapoli" => "Hematologi Onkologi Medik",
  //   "pasienbaru" => 0,
  //   "norm" => 287717,
  //   "tanggalperiksa" => "2022-11-11",
  //   "kodedokter" => 216749,
  //   "namadokter" => "dr. RES<PERSON> SARI, Sp.<PERSON>,<PERSON>H<PERSON>",
  //   "jampraktek" => "08:00-16:00",
  //   "jeniskunjungan" => 1,
  //   "nomorreferensi" => "0001R0040116A000001",
  //   "nomorantrean" => "1",
  //   "angkaantrean" => 1,
  //   "estimasidilayani" => 1667153700000,
  //   "sisakuotajkn" => 25,
  //   "kuotajkn" => 26,
  //   "sisakuotanonjkn" => 25,
  //   "kuotanonjkn" => 26,
  //   "keterangan" => "Peserta harap 60 menit lebih awal guna pencatatan administrasi."
  // );
  
  // $response = $helper->send(json_encode($data));
  $tanggal = date('Y-m-d', strtotime('+1 day'));
  // echo $tanggal;die();
  // $tanggal = date('Y-m-d');
  $queryPerjanjian = "CALL `ws_scheduler_antrean`('$tanggal')";
  $dataPerjanjian = $conn->query($queryPerjanjian);
  $conn->next_result();
  if ($dataPerjanjian->num_rows > 0) {
    // output data of each row
    $array = array();
    $log = "";
    while($row = $dataPerjanjian->fetch_assoc()) {
      // echo $row['nomorkartu'].'<br/>';
      // $nomorreferensi = $row['nomorreferensi'];
      // $jenisKunjungan = $row['jeniskunjungan'];
      // if($nomorreferensi === null){
      //   $rujukan = $helper->send(json_encode(["noKartu" => $row['nomorkartu']]),'vclaimv2/rujukan/rs','GET');
      //   $resRujukan = json_decode($rujukan)->response;
        
      //   if($resRujukan->metaData->code != 200){
      //     $rujukan = $helper->send(json_encode(["noKartu" => $row['nomorkartu']]),'vclaimv2/rujukan/pcare','GET');
      //     $resRujukan = json_decode($rujukan)->response;
      //     if($resRujukan->metaData->code != 200){
      //       continue;
      //     }
      //   }
      //   $nomorreferensi = $resRujukan->response->rujukan[0]->noKunjungan;
      //   $jenisKunjungan = 2;
      // }
      // echo $nomorreferensi.'<br/>';
      $data = array(
        "kodebooking" => $row['kodebooking'],
        "jenispasien" => $row['jenispasien'],
        "nomorkartu" => $row['nomorkartu'],
        "nik" => $row['nik'],
        "nohp" => $row['nohp'],
        "kodepoli" => $row['kodepoli'],
        "namapoli" => $row['namapoli'],
        "pasienbaru" => $row['pasienbaru'],
        "norm" => $row['norm'],
        "tanggalperiksa" => $row['tanggalperiksa'],
        "kodedokter" => $row['kodedokter'],
        "namadokter" => $row['namadokter'],
        "jampraktek" => $row['jampraktek'],
        "jeniskunjungan" => $row['jeniskunjungan'],
        "nomorreferensi" => $row['nomorreferensi'],
        "nomorantrean" => $row['nomorantrean'],
        "angkaantrean" => $row['angkaantrean'],
        "estimasidilayani" => $row['estimasidilayani'],
        "sisakuotajkn" => $row['sisakuotajkn'],
        "kuotajkn" => $row['kuotajkn'],
        "sisakuotanonjkn" => $row['sisakuotanonjkn'],
        "kuotanonjkn" => $row['kuotanonjkn'],
        "keterangan" => $row['keterangan']
      );
      echo $row['kodebooking'];
      $response = $helper->send(json_encode($data),'antrean/add');
      $res = json_decode($response);
      $kodebooking = $row['kodebooking'];
      $request = json_encode($data);
      $code = $res->response->metadata->code;
      $uuid = $helper->guidv4();
      
      $log = "REPLACE INTO remun_medis.log_add_antrean (uuid, id_perjanjian, request, response, code) VALUES ('$uuid',$kodebooking, '$request', '$response', '$code');";
      if ($conn->multi_query($log) === TRUE) {
        echo " New records created successfully \n";
      } else {
        echo "Error: " . $log . "<br>" . $conn->error;
      }
      // sleep(1);
    }
  } else {
    echo "0 results";
  }
  $conn->close();
  // echo json_encode($array);