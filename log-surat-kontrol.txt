2024-06-20 10:35:47 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","3 - ANA - 030"],"nomorkartu":"0001592081684","tanggal":"2024-06-20"}
2024-06-20 10:36:20 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan PCARE"," - ANA - ORT"],"nomorkartu":"0002297483324","tanggal":"2024-06-20"}
2024-06-20 10:36:40 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","1 - ANA - 017"],"nomorkartu":"0003341343925","tanggal":"2024-06-20"}
2024-06-20 10:37:01 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","1 - ANA - 030"],"nomorkartu":"0003094262245","tanggal":"2024-06-20"}
2024-06-20 10:37:48 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","8 - ANA - ANA"],"nomorkartu":"0001201605704","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V001805","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"201","message":"Poli Tujuan Kontrol Tidak Valid untuk SEP tersebut!"},"response":null}}
2024-06-20 10:38:18 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","3 - ANA - 030"],"nomorkartu":"0001172088876","tanggal":"2024-06-20"}
2024-06-20 10:38:40 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","4 - ANA - 030"],"nomorkartu":"0002503296505","tanggal":"2024-06-20"}
2024-06-20 10:39:20 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","6 - ANA - ANA"],"nomorkartu":"0002354701869","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007638","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"201","message":"Poli Tujuan Kontrol Tidak Valid untuk SEP tersebut!"},"response":null}}
2024-06-20 10:39:55 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan PCARE"," - ANA - 021"],"nomorkartu":"0003342921052","tanggal":"2024-06-20"}
2024-06-20 10:40:33 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan PCARE"," - ANA - 030"],"nomorkartu":"0003264585208","tanggal":"2024-06-20"}
2024-06-20 10:41:23 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","8 - ANA - ANA"],"nomorkartu":"0002356460752","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007148","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"201","message":"Poli Tujuan Kontrol Tidak Valid untuk SEP tersebut!"},"response":null}}
2024-06-20 10:41:50 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","9 - ANA - MAT"],"nomorkartu":"0003500149588","tanggal":"2024-06-20"}
2024-06-20 10:42:10 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","6 - ANA - 030"],"nomorkartu":"0003103295466","tanggal":"2024-06-20"}
2024-06-20 10:42:41 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","1 - ANA - 030"],"nomorkartu":"0003313270844","tanggal":"2024-06-20"}
2024-06-20 10:43:18 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","2 - ANA - 030"],"nomorkartu":"0002909073677","tanggal":"2024-06-20"}
2024-06-20 10:43:53 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan PCARE"," - ANA - KEM"],"nomorkartu":"0001464463315","tanggal":"2024-06-20"}
2024-06-20 10:44:21 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","8 - ANA - 030"],"nomorkartu":"0002330662274","tanggal":"2024-06-20"}
2024-06-20 10:45:10 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","1 - ANA - ANA"],"nomorkartu":"0002888766461","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017033","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 10:46:01 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","1 - ANA - ANA"],"nomorkartu":"0003136598853","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016790","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 10:46:39 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","8 - ANA - ANA"],"nomorkartu":"0001602715522","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V008902","kodeDokter":"381235","poliKontrol":"ANA","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"201","message":"Poli Tujuan Kontrol Tidak Valid untuk SEP tersebut!"},"response":null}}
2024-06-20 10:47:07 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","0 - ANA - 030"],"nomorkartu":"0003288625468","tanggal":"2024-06-20"}
2024-06-20 10:47:29 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","7 - ANA - 030"],"nomorkartu":"0001610170187","tanggal":"2024-06-20"}
2024-06-20 10:47:49 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","7 - ANA - 030"],"nomorkartu":"0001624287249","tanggal":"2024-06-20"}
2024-06-20 10:48:09 {"catatan":["Dokter DPJP di ubah dari 35255 menjadi 381235","Rujukan RS","14 - ANA - 030"],"nomorkartu":"0002698257352","tanggal":"2024-06-20"}
2024-06-20 10:48:39 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","1 - INT - THT"],"nomorkartu":"0001463004876","tanggal":"2024-06-20"}
2024-06-20 10:49:11 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - 017"],"nomorkartu":"0000002896852","tanggal":"2024-06-20"}
2024-06-20 10:49:52 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - INT"],"nomorkartu":"0001376168861","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017100","kodeDokter":"1647","poliKontrol":"INT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 10:51:01 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","2 - INT - URO"],"nomorkartu":"0000037303165","tanggal":"2024-06-20"}
2024-06-20 10:51:25 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","10 - INT - 017"],"nomorkartu":"0001273405724","tanggal":"2024-06-20"}
2024-06-20 10:52:16 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","12 - INT - INT"],"nomorkartu":"0001482348205","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018056","kodeDokter":"1647","poliKontrol":"INT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 10:52:53 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS"," - INT - 008"],"nomorkartu":"0001645452764","tanggal":"2024-06-20"}
2024-06-20 10:53:30 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","7 - INT - 021"],"nomorkartu":"0002342633444","tanggal":"2024-06-20"}
2024-06-20 10:53:59 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","16 - INT - 021"],"nomorkartu":"0002321824498","tanggal":"2024-06-20"}
2024-06-20 10:54:41 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0001868607358","tanggal":"2024-06-20"}
2024-06-20 10:55:05 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","4 - INT - THT"],"nomorkartu":"0002332447075","tanggal":"2024-06-20"}
2024-06-20 10:55:29 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","7 - INT - 008"],"nomorkartu":"0000005163129","tanggal":"2024-06-20"}
2024-06-20 10:55:51 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0001196607947","tanggal":"2024-06-20"}
2024-06-20 10:56:16 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","5 - INT - 018"],"nomorkartu":"0000039483303","tanggal":"2024-06-20"}
2024-06-20 10:56:37 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","10 - INT - OBG"],"nomorkartu":"0000022541477","tanggal":"2024-06-20"}
2024-06-20 10:57:00 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","4 - INT - 017"],"nomorkartu":"0001616333051","tanggal":"2024-06-20"}
2024-06-20 10:57:31 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0001818860758","tanggal":"2024-06-20"}
2024-06-20 10:58:18 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - HDL"],"nomorkartu":"0000370602358","tanggal":"2024-06-20"}
2024-06-20 10:59:08 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan PCARE"," - INT - RAT"],"nomorkartu":"0001724653427","tanggal":"2024-06-20"}
2024-06-20 10:59:44 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","8 - INT - 017"],"nomorkartu":"0001643039245","tanggal":"2024-06-20"}
2024-06-20 11:00:57 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","6 - INT - 017"],"nomorkartu":"0002209736035","tanggal":"2024-06-20"}
2024-06-20 11:01:32 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - 021"],"nomorkartu":"0001219154488","tanggal":"2024-06-20"}
2024-06-20 11:02:32 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan PCARE"," - INT - 017"],"nomorkartu":"0001807345541","tanggal":"2024-06-20"}
2024-06-20 11:02:57 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","6 - INT - 017"],"nomorkartu":"0000045370945","tanggal":"2024-06-20"}
2024-06-20 11:03:24 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","5 - INT - OBG"],"nomorkartu":"0000395302904","tanggal":"2024-06-20"}
2024-06-20 11:03:42 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","0 - INT - 018"],"nomorkartu":"0003149134817","tanggal":"2024-06-20"}
2024-06-20 11:04:04 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0000042244738","tanggal":"2024-06-20"}
2024-06-20 11:04:40 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","36 - INT - 017"],"nomorkartu":"0002511961457","tanggal":"2024-06-20"}
2024-06-20 11:05:17 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - 017"],"nomorkartu":"0000168390257","tanggal":"2024-06-20"}
2024-06-20 11:07:15 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0001438160073","tanggal":"2024-06-20"}
2024-06-20 11:07:35 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","6 - INT - 017"],"nomorkartu":"0002239288571","tanggal":"2024-06-20"}
2024-06-20 11:07:55 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","2 - INT - THT"],"nomorkartu":"0000044570913","tanggal":"2024-06-20"}
2024-06-20 11:08:18 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","14 - INT - 018"],"nomorkartu":"0000043588822","tanggal":"2024-06-20"}
2024-06-20 11:08:39 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - 017"],"nomorkartu":"0002289406364","tanggal":"2024-06-20"}
2024-06-20 11:09:10 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","2 - INT - 017"],"nomorkartu":"0001284865402","tanggal":"2024-06-20"}
2024-06-20 11:09:31 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","15 - INT - 007"],"nomorkartu":"0001267812876","tanggal":"2024-06-20"}
2024-06-20 11:09:55 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","1 - INT - 007"],"nomorkartu":"0001606240833","tanggal":"2024-06-20"}
2024-06-20 11:10:27 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","6 - INT - 021"],"nomorkartu":"0000049402192","tanggal":"2024-06-20"}
2024-06-20 11:10:49 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","27 - INT - URO"],"nomorkartu":"0002447560405","tanggal":"2024-06-20"}
2024-06-20 11:11:18 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","10 - INT - 017"],"nomorkartu":"0002096108807","tanggal":"2024-06-20"}
2024-06-20 11:11:35 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","0 - INT - THT"],"nomorkartu":"0002479709114","tanggal":"2024-06-20"}
2024-06-20 11:12:08 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - HIV"],"nomorkartu":"0001220830942","tanggal":"2024-06-20"}
2024-06-20 11:12:26 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","3 - INT - 018"],"nomorkartu":"0000033616427","tanggal":"2024-06-20"}
2024-06-20 11:12:48 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","3 - INT - URO"],"nomorkartu":"0000817985463","tanggal":"2024-06-20"}
2024-06-20 11:13:15 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - 008"],"nomorkartu":"0001282582001","tanggal":"2024-06-20"}
2024-06-20 11:13:36 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - URO"],"nomorkartu":"0001868881904","tanggal":"2024-06-20"}
2024-06-20 11:13:57 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","10 - INT - 017"],"nomorkartu":"0001325419288","tanggal":"2024-06-20"}
2024-06-20 11:14:19 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","29 - INT - 007"],"nomorkartu":"0001723289905","tanggal":"2024-06-20"}
2024-06-20 11:14:43 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - INT"],"nomorkartu":"0001224845324","tanggal":"2024-06-20"}
2024-06-20 11:15:04 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - HDL"],"nomorkartu":"0001603704532","tanggal":"2024-06-20"}
2024-06-20 11:15:26 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan RS","15 - INT - 021"],"nomorkartu":"0003259524576","tanggal":"2024-06-20"}
2024-06-20 11:15:47 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","9 - INT - 017"],"nomorkartu":"0001614086346","tanggal":"2024-06-20"}
2024-06-20 11:16:04 {"catatan":["Dokter DPJP di ubah dari 273370 menjadi 1647","Rujukan RS","6 - INT - 017"],"nomorkartu":"0002223692076","tanggal":"2024-06-20"}
2024-06-20 11:16:27 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - RAT"],"nomorkartu":"0001655269435","tanggal":"2024-06-20"}
2024-06-20 11:16:54 {"catatan":["Dokter DPJP di ubah dari 38185 menjadi 1647","Rujukan PCARE"," - INT - KEM"],"nomorkartu":"0001257392823","tanggal":"2024-06-20"}
2024-06-20 11:17:10 {"nomorkartu":"0001212582071","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - MAT - 017"]}
2024-06-20 11:17:26 {"nomorkartu":"0001269834579","tanggal":"2024-06-20","catatan":["Rujukan RS","18 - MAT - THT"]}
2024-06-20 11:17:42 {"nomorkartu":"0001789114307","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - MAT - INT"]}
2024-06-20 11:18:02 {"nomorkartu":"0001892643592","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - MAT - BSY"]}
2024-06-20 11:18:20 {"nomorkartu":"0001000996839","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - JIW - 008"]}
2024-06-20 11:18:49 {"nomorkartu":"0002331663502","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - JIW - 005"]}
2024-06-20 11:19:10 {"nomorkartu":"0001971101193","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JIW - 017"]}
2024-06-20 11:19:28 {"nomorkartu":"0001737983272","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - JIW - 017"]}
2024-06-20 11:19:43 {"nomorkartu":"0001385587787","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JIW - URO"]}
2024-06-20 11:20:00 {"nomorkartu":"0000041332566","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - JIW - 017"]}
2024-06-20 11:20:26 {"nomorkartu":"0001320472192","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - JIW - OBG"]}
2024-06-20 11:20:42 {"nomorkartu":"0001767057783","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - JIW - 017"]}
2024-06-20 11:21:00 {"nomorkartu":"0001471299502","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - JIW - 017"]}
2024-06-20 11:21:17 {"nomorkartu":"0001222361763","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - JIW - 008"]}
2024-06-20 11:21:38 {"nomorkartu":"0002338173549","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - JIW - 017"]}
2024-06-20 11:22:07 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0001744931226","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016908","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:22:33 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0001641421596","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017255","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:23:12 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","38 - THT - THT"],"nomorkartu":"0002226665755","tanggal":"2024-06-20","request":{"noSEP":null,"kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"401","message":"Transaksi tidak dapat diproses"},"response":null}}
2024-06-20 11:23:37 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0002103569054","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017270","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:24:02 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","7 - THT - THT"],"nomorkartu":"0002252710034","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017490","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:24:29 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0000024884245","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V028005","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:24:54 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","5 - THT - THT"],"nomorkartu":"0002053934548","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017252","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:25:24 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","5 - THT - THT"],"nomorkartu":"0000375632987","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017564","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:25:52 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0000166615615","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V013763","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:26:24 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","17 - THT - THT"],"nomorkartu":"0001854252213","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016946","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:26:54 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","8 - THT - THT"],"nomorkartu":"0001256813594","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017616","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:27:19 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","4 - THT - THT"],"nomorkartu":"0001315230996","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V015411","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:27:44 {"nomorkartu":"0001471913853","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - THT - THT"],"request":{"noSEP":"0904R0080624V009379","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:28:04 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","1 - THT - 017"],"nomorkartu":"0001086580754","tanggal":"2024-06-20"}
2024-06-20 11:28:19 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","9 - THT - 017"],"nomorkartu":"0003580946594","tanggal":"2024-06-20"}
2024-06-20 11:28:33 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","2 - THT - 017"],"nomorkartu":"0000806451513","tanggal":"2024-06-20"}
2024-06-20 11:28:58 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","4 - THT - THT"],"nomorkartu":"0002214219723","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016893","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:29:24 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","7 - THT - THT"],"nomorkartu":"0001459902611","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017303","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:29:38 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","0 - THT - THT"],"nomorkartu":"0001381501708","tanggal":"2024-06-20"}
2024-06-20 11:30:38 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","6 - THT - THT"],"nomorkartu":"0001404458728","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017448","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:31:16 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","11 - THT - THT"],"nomorkartu":"0001775299206","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017966","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:31:55 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","9 - THT - THT"],"nomorkartu":"0002378319647","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017283","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:32:31 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan PCARE"," - THT - 030"],"nomorkartu":"0002335801386","tanggal":"2024-06-20"}
2024-06-20 11:32:59 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","6 - THT - THT"],"nomorkartu":"0001458364937","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018036","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:33:23 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","8 - THT - THT"],"nomorkartu":"0001200825246","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017565","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:33:47 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0002902906168","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V012187","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:34:10 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","13 - THT - THT"],"nomorkartu":"0001730939499","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017068","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:34:37 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","4 - THT - THT"],"nomorkartu":"0003521960774","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016880","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:34:51 {"nomorkartu":"0000617014528","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - THT - 017"]}
2024-06-20 11:35:26 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","10 - THT - THT"],"nomorkartu":"0001158647477","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V012309","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:35:39 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","5 - THT - 017"],"nomorkartu":"0002139491002","tanggal":"2024-06-20"}
2024-06-20 11:35:58 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","0 - THT - THT"],"nomorkartu":"0002299649927","tanggal":"2024-06-20"}
2024-06-20 11:36:13 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","2 - THT - SAR"],"nomorkartu":"0000201445626","tanggal":"2024-06-20"}
2024-06-20 11:36:31 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan PCARE"," - THT - SAR"],"nomorkartu":"0003531808912","tanggal":"2024-06-20"}
2024-06-20 11:36:55 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0000169924004","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V019206","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006375","tglRencanaKontrol":"2024-06-20","namaDokter":"HJ. CITA HERAWATI","noKartu":"0000169924004","nama":"MUHIMIN","kelamin":"Laki-laki","tglLahir":"1977-07-18","namaDiagnosa":"C11.3 - Malignant neoplasm of anterior wall of nasopharynx"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080524V019206', '217782', 'THT', '0904R0080624K006375', '0000169924004', 'HJ. CITA HERAWATI, DR.,dr., SpTHT-KL', '2024-06-20', 'MUHIMIN (ROJUDIN), TN', '1065')<br>MySQL server has gone away"}
2024-06-20 11:37:25 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","3 - THT - THT"],"nomorkartu":"0001212865582","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V025642","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:37:52 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","8 - THT - URO"],"nomorkartu":"0000044564207","tanggal":"2024-06-20"}
2024-06-20 11:38:10 {"nomorkartu":"0001801326442","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - THT - URO"]}
2024-06-20 11:38:30 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","11 - THT - SAR"],"nomorkartu":"0001307978054","tanggal":"2024-06-20"}
2024-06-20 11:38:58 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","25 - THT - THT"],"nomorkartu":"0000015335921","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V002244","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Inap Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:39:32 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","7 - THT - THT"],"nomorkartu":"0001968901942","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017384","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:39:59 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0001458800493","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V013348","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:40:35 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0001600295578","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017703","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:41:12 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0001443460544","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017390","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:41:30 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","0 - THT - THT"],"nomorkartu":"0001390439147","tanggal":"2024-06-20"}
2024-06-20 11:42:04 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","5 - THT - THT"],"nomorkartu":"0001095184282","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017791","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:42:27 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0001773815589","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V011435","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:42:58 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan PCARE"," - THT - 021"],"nomorkartu":"0003079658114","tanggal":"2024-06-20"}
2024-06-20 11:43:21 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","5 - THT - THT"],"nomorkartu":"0000228904042","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017268","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:43:43 {"catatan":["Dokter DPJP di ubah dari 217783 menjadi 217782","Rujukan RS","4 - THT - THT"],"nomorkartu":"0000173673955","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017398","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:43:56 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","2 - THT - 017"],"nomorkartu":"0001862653667","tanggal":"2024-06-20"}
2024-06-20 11:44:26 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","8 - THT - THT"],"nomorkartu":"0002335137096","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018064","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:44:52 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","1 - THT - THT"],"nomorkartu":"0001792642724","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017223","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:45:31 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","2 - THT - THT"],"nomorkartu":"0003175528408","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017820","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:45:56 {"catatan":["Dokter DPJP di ubah dari 217794 menjadi 217782","Rujukan RS","1 - THT - 008"],"nomorkartu":"0003074109131","tanggal":"2024-06-20"}
2024-06-20 11:46:33 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","3 - THT - THT"],"nomorkartu":"0002276680026","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017762","kodeDokter":"217782","poliKontrol":"THT","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:46:50 {"catatan":["Dokter DPJP di ubah dari 392411 menjadi 217782","Rujukan RS","6 - THT - 008"],"nomorkartu":"0001257336887","tanggal":"2024-06-20"}
2024-06-20 11:47:26 {"nomorkartu":"0001738760488","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - BSY - BSY"],"request":{"noSEP":"0904R0080624V017717","kodeDokter":"217604","poliKontrol":"BSY","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:47:41 {"catatan":["Dokter DPJP di ubah dari 217610 menjadi 217604","Rujukan RS","11 - BSY - PAR"],"nomorkartu":"0001469454873","tanggal":"2024-06-20"}
2024-06-20 11:47:57 {"catatan":["Dokter DPJP di ubah dari 27124 menjadi 217604","Rujukan RS","6 - BSY - SAR"],"nomorkartu":"0001870914238","tanggal":"2024-06-20"}
2024-06-20 11:48:37 {"nomorkartu":"0001854070986","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - BSY - BSY"],"request":{"noSEP":"0904R0080624V013089","kodeDokter":"217604","poliKontrol":"BSY","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:48:50 {"nomorkartu":"0001459994376","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - BSY - SAR"]}
2024-06-20 11:49:11 {"nomorkartu":"0002313027303","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - BSY - PAR"]}
2024-06-20 11:49:48 {"nomorkartu":"0001906141869","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - BSY - BSY"],"request":{"noSEP":"0904R0080624V017790","kodeDokter":"217604","poliKontrol":"BSY","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:50:02 {"nomorkartu":"0002625146111","tanggal":"2024-06-20","catatan":["Rujukan RS","27 - BSY - 030"]}
2024-06-20 11:50:29 {"nomorkartu":"0000205411735","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - BSY - BSY"]}
2024-06-20 11:50:44 {"nomorkartu":"0000043500396","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - KLT - 017"]}
2024-06-20 11:51:03 {"nomorkartu":"0002481467027","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - KLT - BSY"]}
2024-06-20 11:51:18 {"nomorkartu":"0001904107239","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - KLT - THT"]}
2024-06-20 11:51:36 {"nomorkartu":"0002750153229","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - KLT - 017"]}
2024-06-20 11:51:57 {"nomorkartu":"0001968754713","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - KLT - 017"]}
2024-06-20 11:52:11 {"nomorkartu":"0001396687419","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - KLT - PAR"]}
2024-06-20 11:52:39 {"nomorkartu":"0001962926133","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - SAR - SAR"],"request":{"noSEP":"0904R0080624V016288","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 11:52:53 {"nomorkartu":"0001214862096","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - SAR - INT"]}
2024-06-20 11:53:20 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan PCARE"," - SAR - SAR"],"nomorkartu":"0000038221615","tanggal":"2024-06-20"}
2024-06-20 11:53:32 {"nomorkartu":"0001259029078","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - SAR - 017"]}
2024-06-20 11:53:45 {"nomorkartu":"0001729273779","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - SAR - PAR"]}
2024-06-20 11:54:00 {"nomorkartu":"0002340354587","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - SAR - URO"]}
2024-06-20 11:54:18 {"nomorkartu":"0001959771802","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - SAR - 017"]}
2024-06-20 11:54:32 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","1 - SAR - 017"],"nomorkartu":"0000033308043","tanggal":"2024-06-20"}
2024-06-20 11:54:53 {"nomorkartu":"0001718395312","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - SAR - 017"]}
2024-06-20 11:55:24 {"nomorkartu":"0001504176579","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - SAR - SAR"],"request":{"noSEP":"0904R0080624V017130","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:55:46 {"nomorkartu":"0001786395093","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - SAR - SAR"],"request":{"noSEP":"0904R0080624V017923","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:56:18 {"nomorkartu":"0001829638247","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - SAR - SAR"],"request":{"noSEP":"0904R0080524V028207","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Nomor SEP Sudah pernah digunakan!."},"response":null}}
2024-06-20 11:56:34 {"nomorkartu":"0002304952907","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - SAR - PAR"]}
2024-06-20 11:56:58 {"nomorkartu":"0002968586447","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - SAR - SAR"],"request":{"noSEP":"0904R0080624V017597","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:57:18 {"nomorkartu":"0001438599014","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - SAR - KEM"]}
2024-06-20 11:57:36 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","7 - SAR - SAR"],"nomorkartu":"0001811312008","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017366","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 11:57:51 {"nomorkartu":"0000042922001","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - SAR - URO"]}
2024-06-20 11:58:02 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","9 - SAR - 030"],"nomorkartu":"0002354775783","tanggal":"2024-06-20"}
2024-06-20 11:58:13 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","10 - SAR - 017"],"nomorkartu":"0001409317187","tanggal":"2024-06-20"}
2024-06-20 11:58:21 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","1 - SAR - 008"],"nomorkartu":"0000062135818","tanggal":"2024-06-20"}
2024-06-20 11:58:31 {"nomorkartu":"0000624857207","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - SAR - PAR"]}
2024-06-20 11:58:40 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","18 - SAR - 017"],"nomorkartu":"0002878613346","tanggal":"2024-06-20"}
2024-06-20 11:58:45 {"nomorkartu":"0000163254148","tanggal":"2024-06-20","catatan":["Rujukan RS","11 - SAR - 008"]}
2024-06-20 11:58:56 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan PCARE"," - SAR - 017"],"nomorkartu":"0001282902197","tanggal":"2024-06-20"}
2024-06-20 11:59:05 {"nomorkartu":"0000171762208","tanggal":"2024-06-20","catatan":["Rujukan RS","11 - SAR - 017"]}
2024-06-20 11:59:14 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","1 - SAR - BSY"],"nomorkartu":"0001788239125","tanggal":"2024-06-20"}
2024-06-20 11:59:19 {"nomorkartu":"0001454503994","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - SAR - THT"]}
2024-06-20 11:59:30 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan PCARE"," - SAR - BSY"],"nomorkartu":"0000998565287","tanggal":"2024-06-20"}
2024-06-20 11:59:37 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","2 - SAR - THT"],"nomorkartu":"0001603354781","tanggal":"2024-06-20"}
2024-06-20 11:59:50 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","35 - SAR - BSY"],"nomorkartu":"0002623283695","tanggal":"2024-06-20"}
2024-06-20 11:59:58 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","12 - SAR - SAR"],"nomorkartu":"0000375227155","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017394","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:00:10 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","5 - SAR - INT"],"nomorkartu":"0001434467259","tanggal":"2024-06-20"}
2024-06-20 12:00:21 {"nomorkartu":"0000526716235","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - SAR - SAR"],"request":{"noSEP":"0904R0080624V018196","kodeDokter":"217777","poliKontrol":"SAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:00:32 {"catatan":["Dokter DPJP di ubah dari 374685 menjadi 217777","Rujukan RS","25 - SAR - BSY"],"nomorkartu":"0001453667095","tanggal":"2024-06-20"}
2024-06-20 12:00:39 {"nomorkartu":"0002756483212","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - SAR - 017"]}
2024-06-20 12:00:45 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","12 - JAN - THT"],"nomorkartu":"0000011759275","tanggal":"2024-06-20"}
2024-06-20 12:00:55 {"nomorkartu":"0001173688288","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - JAN - 021"]}
2024-06-20 12:01:12 {"nomorkartu":"0001593236935","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - JAN - 017"]}
2024-06-20 12:01:20 {"nomorkartu":"0001600742013","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - JAN - 018"]}
2024-06-20 12:01:25 {"nomorkartu":"0001294132983","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JAN - 008"]}
2024-06-20 12:01:32 {"nomorkartu":"0000801342448","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - JAN - 017"]}
2024-06-20 12:01:37 {"nomorkartu":"0002143460878","tanggal":"2024-06-20","catatan":["Rujukan RS","18 - JAN - 017"]}
2024-06-20 12:01:44 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan PCARE"," - JAN - 008"],"nomorkartu":"0002924566953","tanggal":"2024-06-20"}
2024-06-20 12:01:49 {"nomorkartu":"0000061153986","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JAN - 017"]}
2024-06-20 12:01:55 {"nomorkartu":"0001221275193","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - JAN - 021"]}
2024-06-20 12:01:59 {"nomorkartu":"0001662368275","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - JAN - 021"]}
2024-06-20 12:02:05 {"nomorkartu":"0000018072303","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - JAN - 021"]}
2024-06-20 12:02:10 {"nomorkartu":"0000038778805","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - JAN - URO"]}
2024-06-20 12:02:19 {"nomorkartu":"0000803192275","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JAN - 017"]}
2024-06-20 12:02:24 {"nomorkartu":"0001438607992","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - JAN - 017"]}
2024-06-20 12:02:28 {"nomorkartu":"0000372986291","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JAN - 008"]}
2024-06-20 12:02:35 {"nomorkartu":"0000367632202","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - JAN - KEM"]}
2024-06-20 12:02:40 {"nomorkartu":"0001663446857","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - JAN - 017"]}
2024-06-20 12:02:47 {"nomorkartu":"0002073846418","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JAN - 017"]}
2024-06-20 12:02:53 {"nomorkartu":"0001258354203","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - JAN - 017"]}
2024-06-20 12:02:59 {"nomorkartu":"0002070465502","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JAN - 017"]}
2024-06-20 12:03:05 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","5 - JAN - 008"],"nomorkartu":"0001382989599","tanggal":"2024-06-20"}
2024-06-20 12:03:15 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","8 - JAN - THT"],"nomorkartu":"0002103168521","tanggal":"2024-06-20"}
2024-06-20 12:03:21 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","7 - JAN - OBG"],"nomorkartu":"0000263711744","tanggal":"2024-06-20"}
2024-06-20 12:03:26 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","3 - JAN - 017"],"nomorkartu":"0001769878315","tanggal":"2024-06-20"}
2024-06-20 12:03:32 {"nomorkartu":"0001222247981","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - JAN - THT"]}
2024-06-20 12:03:38 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","1 - JAN - 021"],"nomorkartu":"0001815654712","tanggal":"2024-06-20"}
2024-06-20 12:03:51 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","1 - JAN - THT"],"nomorkartu":"0001129789956","tanggal":"2024-06-20"}
2024-06-20 12:03:57 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","9 - JAN - 017"],"nomorkartu":"0001801013534","tanggal":"2024-06-20"}
2024-06-20 12:04:11 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","11 - JAN - 017"],"nomorkartu":"0001633050202","tanggal":"2024-06-20"}
2024-06-20 12:04:15 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","19 - JAN - SAR"],"nomorkartu":"0001961256374","tanggal":"2024-06-20"}
2024-06-20 12:04:23 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","11 - JAN - 017"],"nomorkartu":"0002427533818","tanggal":"2024-06-20"}
2024-06-20 12:04:32 {"nomorkartu":"0001208012916","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JAN - OBG"]}
2024-06-20 12:04:37 {"nomorkartu":"0003340600345","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - JAN - 018"]}
2024-06-20 12:04:43 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan PCARE"," - JAN - 017"],"nomorkartu":"0001741080126","tanggal":"2024-06-20"}
2024-06-20 12:04:47 {"nomorkartu":"0002055512845","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - JAN - 008"]}
2024-06-20 12:04:52 {"nomorkartu":"0001297258997","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - JAN - PAR"]}
2024-06-20 12:04:57 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","7 - JAN - 017"],"nomorkartu":"0001654735858","tanggal":"2024-06-20"}
2024-06-20 12:05:01 {"nomorkartu":"0001093730815","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - JAN - 008"]}
2024-06-20 12:05:06 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","5 - JAN - SAR"],"nomorkartu":"0001403951657","tanggal":"2024-06-20"}
2024-06-20 12:05:33 {"nomorkartu":"0001258770969","tanggal":"2024-06-20","catatan":["Rujukan RS"," - JAN - 021"]}
2024-06-20 12:06:14 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","4 - JAN - 017"],"nomorkartu":"0001376401274","tanggal":"2024-06-20"}
2024-06-20 12:06:29 {"nomorkartu":"0002198002026","tanggal":"2024-06-20","catatan":["Rujukan RS","22 - JAN - 017"]}
2024-06-20 12:06:37 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","6 - JAN - URO"],"nomorkartu":"0003002027242","tanggal":"2024-06-20"}
2024-06-20 12:06:43 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","9 - JAN - 017"],"nomorkartu":"0001457671217","tanggal":"2024-06-20"}
2024-06-20 12:06:48 {"nomorkartu":"0001089183161","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - JAN - 008"]}
2024-06-20 12:06:52 {"nomorkartu":"0001213089377","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - JAN - 021"]}
2024-06-20 12:06:56 {"nomorkartu":"0002690760846","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JAN - 017"]}
2024-06-20 12:07:00 {"nomorkartu":"0002625478007","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - JAN - 017"]}
2024-06-20 12:07:03 {"nomorkartu":"0003308520003","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - JAN - 017"]}
2024-06-20 12:07:08 {"catatan":["Dokter DPJP di ubah dari 270390 menjadi 217869","Rujukan RS","5 - JAN - 021"],"nomorkartu":"0001053572714","tanggal":"2024-06-20"}
2024-06-20 12:07:13 {"nomorkartu":"0001728572782","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - JAN - THT"]}
2024-06-20 12:07:16 {"nomorkartu":"0002332465312","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - JAN - 017"]}
2024-06-20 12:07:20 {"catatan":["Dokter DPJP di ubah dari 217845 menjadi 28116","Rujukan RS","1 - BDP - SAR"],"nomorkartu":"0001290378892","tanggal":"2024-06-20"}
2024-06-20 12:07:26 {"catatan":["Dokter DPJP di ubah dari 217845 menjadi 28116","Rujukan PCARE"," - BDP - 008"],"nomorkartu":"0003567106214","tanggal":"2024-06-20"}
2024-06-20 12:07:30 {"catatan":["Dokter DPJP di ubah dari 217845 menjadi 28116","Rujukan RS","6 - BDP - 017"],"nomorkartu":"0001454861834","tanggal":"2024-06-20"}
2024-06-20 12:07:37 {"nomorkartu":"0002330821708","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017018","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:07:44 {"nomorkartu":"0001315341281","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - URO - URO"],"request":{"noSEP":"0904R0080624V017472","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:07:59 {"nomorkartu":"0001484414728","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017043","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:06 {"nomorkartu":"0001386677035","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - URO - URO"],"request":{"noSEP":"0904R0080624V017294","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:13 {"nomorkartu":"0000044035547","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - URO - URO"],"request":{"noSEP":"0904R0080624V016903","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:21 {"nomorkartu":"0001261930173","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017163","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:30 {"nomorkartu":"0003509449481","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - URO - URO"],"request":{"noSEP":"0904R0080624V015921","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:08:37 {"nomorkartu":"0002073746171","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - URO - URO"],"request":{"noSEP":"0904R0080624V017124","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:44 {"nomorkartu":"0002303980986","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017409","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:08:49 {"nomorkartu":"0003580982109","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - 018"]}
2024-06-20 12:08:56 {"nomorkartu":"0002140356172","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - URO - URO"],"request":{"noSEP":"0904R0080624V012268","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:09:03 {"nomorkartu":"0000047313156","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - URO - URO"],"request":{"noSEP":"0904R0080624V017638","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:09:11 {"nomorkartu":"0001318464101","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - URO - URO"],"request":{"noSEP":"0904R0080624V017530","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:09:18 {"nomorkartu":"0001795993806","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - URO - URO"],"request":{"noSEP":"0904R0080624V017387","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:09:25 {"nomorkartu":"0002255498695","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - URO - URO"],"request":{"noSEP":"0904R0080624V017692","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:09:34 {"nomorkartu":"0000046643602","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017189","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:09:45 {"nomorkartu":"0001338237235","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - URO - 021"]}
2024-06-20 12:09:50 {"nomorkartu":"0002038562054","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - URO - URO"]}
2024-06-20 12:09:56 {"nomorkartu":"0001221289571","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - URO - URO"]}
2024-06-20 12:10:05 {"nomorkartu":"0001621904455","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - URO - URO"],"request":{"noSEP":"0904R0080624V017765","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:10:18 {"nomorkartu":"0000158049325","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - URO - URO"],"request":{"noSEP":"0904R0080624V017508","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:10:26 {"nomorkartu":"0001592698915","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - URO - URO"],"request":{"noSEP":"0904R0080624V017743","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:10:32 {"nomorkartu":"0001457601614","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - URO - URO"]}
2024-06-20 12:10:42 {"nomorkartu":"0000039356201","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017922","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:10:50 {"nomorkartu":"0000148531634","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - URO - URO"]}
2024-06-20 12:10:56 {"nomorkartu":"0001296585224","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - URO - URO"]}
2024-06-20 12:11:03 {"nomorkartu":"0000059419135","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V016872","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:10 {"nomorkartu":"0001401850978","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - URO - URO"],"request":{"noSEP":"0904R0080624V017271","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:18 {"nomorkartu":"0000066670727","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - URO - URO"],"request":{"noSEP":"0904R0080624V017437","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:25 {"nomorkartu":"0001733740661","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - URO - URO"],"request":{"noSEP":"0904R0080624V017969","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:32 {"nomorkartu":"0002620302963","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080524V026167","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:11:39 {"nomorkartu":"0001425017529","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - URO - URO"],"request":{"noSEP":"0904R0080624V017212","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:48 {"nomorkartu":"0001856573919","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017267","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:11:56 {"catatan":["Dokter DPJP di ubah dari 217342 menjadi 125961","Rujukan RS","2 - URO - URO"],"nomorkartu":"0001324520313","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017903","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:01 {"nomorkartu":"0001717280144","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - URO - 021"]}
2024-06-20 12:12:11 {"nomorkartu":"0001214736491","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - URO - URO"],"request":{"noSEP":"0904R0080624V017281","kodeDokter":"125961","poliKontrol":"URO","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:17 {"nomorkartu":"0002919443049","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 018"],"request":{"noSEP":"0904R0080624V016769","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:21 {"nomorkartu":"0001457644105","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 018 - 008"]}
2024-06-20 12:12:31 {"nomorkartu":"0001325617097","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 018 - 018"],"request":{"noSEP":"0904R0080624V016854","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:37 {"nomorkartu":"0001742830345","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 018"],"request":{"noSEP":"0904R0080624V017513","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:44 {"nomorkartu":"0002688169037","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 018"],"request":{"noSEP":"0904R0080624V017191","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:50 {"nomorkartu":"0003174376577","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 018"],"request":{"noSEP":"0904R0080624V017882","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:12:57 {"nomorkartu":"0001611984914","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - 018"],"request":{"noSEP":"0904R0080624V017327","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:13:00 {"nomorkartu":"0001086642516","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 017"]}
2024-06-20 12:13:03 {"nomorkartu":"0000048037825","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 017"]}
2024-06-20 12:13:06 {"catatan":["Dokter DPJP di ubah dari 301150 menjadi 211860","Rujukan RS","8 - 018 - 017"],"nomorkartu":"0001791184904","tanggal":"2024-06-20"}
2024-06-20 12:13:13 {"nomorkartu":"0000039337446","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 018"],"request":{"noSEP":"0904R0080624V017356","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:13:17 {"nomorkartu":"0001215054898","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 008"]}
2024-06-20 12:13:24 {"nomorkartu":"0001312437093","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 018 - 008"]}
2024-06-20 12:13:30 {"nomorkartu":"0001717746568","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 018 - KEM"]}
2024-06-20 12:13:35 {"nomorkartu":"0001268482206","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - 017"]}
2024-06-20 12:13:45 {"nomorkartu":"0001771614347","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 018 - 018"]}
2024-06-20 12:13:55 {"nomorkartu":"0001631254937","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - 018"],"request":{"noSEP":"0904R0080624V010531","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:14:06 {"nomorkartu":"0002316178258","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 018"],"request":{"noSEP":"0904R0080624V016836","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:15 {"nomorkartu":"0000045690254","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 018 - 018"],"request":{"noSEP":"0904R0080624V016841","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:22 {"nomorkartu":"0000428237469","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 018"],"request":{"noSEP":"0904R0080624V016784","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:31 {"nomorkartu":"0001797551008","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - 018"],"request":{"noSEP":"0904R0080624V017546","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:37 {"nomorkartu":"0001460153193","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 018 - 018"]}
2024-06-20 12:14:46 {"nomorkartu":"0000811487665","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - 018"],"request":{"noSEP":"0904R0080624V017035","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:49 {"nomorkartu":"0001455531412","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - THT"]}
2024-06-20 12:14:55 {"nomorkartu":"0002584841242","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 018 - 018"],"request":{"noSEP":"0904R0080624V018081","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:14:59 {"nomorkartu":"0001291682621","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 017"]}
2024-06-20 12:15:07 {"nomorkartu":"0000072875766","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - 018 - 018"],"request":{"noSEP":"0904R0080624V015666","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006387","tglRencanaKontrol":"2024-06-20","namaDokter":"FAJAR FIRSYADA","noKartu":"0000072875766","nama":"MAHARADJA SAJUTI LUBIS","kelamin":"Laki-laki","tglLahir":"1955-08-16","namaDiagnosa":"C20 - Malignant neoplasm of rectum"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V015666', '211860', '018', '0904R0080624K006387', '0000072875766', 'dr. FAJAR FIRSYADA DR SPB KBD', '2024-06-20', 'MAHARADJA SAYUTI LUBIS, H, TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:15:12 {"nomorkartu":"0001335463481","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 018 - INT"]}
2024-06-20 12:15:20 {"nomorkartu":"0002294198357","tanggal":"2024-06-20","catatan":["Rujukan RS","16 - 018 - 018"],"request":{"noSEP":"0904R0080624V017200","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:15:27 {"nomorkartu":"0001463708283","tanggal":"2024-06-20","catatan":["Rujukan RS","17 - 018 - 018"],"request":{"noSEP":"0904R0080624V011147","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006388","tglRencanaKontrol":"2024-06-20","namaDokter":"FAJAR FIRSYADA","noKartu":"0001463708283","nama":"T KAMTORO","kelamin":"Laki-laki","tglLahir":"1958-07-09","namaDiagnosa":"C18.7 - Malignant neoplasm of sigmoid colon"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V011147', '211860', '018', '0904R0080624K006388', '0001463708283', 'dr. FAJAR FIRSYADA DR SPB KBD', '2024-06-20', 'KAMTORO, TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:15:31 {"nomorkartu":"0001458199653","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - 021"]}
2024-06-20 12:15:44 {"nomorkartu":"0000024177328","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - 018"],"request":{"noSEP":"0904R0080624V017320","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:15:47 {"nomorkartu":"0001259045943","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 017"]}
2024-06-20 12:16:04 {"nomorkartu":"0001372831547","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - 018"],"request":{"noSEP":"0904R0080624V017654","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:16:12 {"nomorkartu":"0001298993758","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 018 - 018"],"request":{"noSEP":"0904R0080624V017520","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:16:23 {"catatan":["Dokter DPJP di ubah dari 301150 menjadi 211860","Rujukan RS","5 - 018 - 018"],"nomorkartu":"0001875693993","tanggal":"2024-06-20","request":{"noSEP":null,"kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"401","message":"Transaksi tidak dapat diproses"},"response":null}}
2024-06-20 12:16:30 {"nomorkartu":"0001298698323","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 018"],"request":{"noSEP":"0904R0080624V018000","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:16:33 {"nomorkartu":"0001800426306","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - INT"]}
2024-06-20 12:16:40 {"nomorkartu":"0001202651076","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 018 - 017"]}
2024-06-20 12:17:00 {"nomorkartu":"0003284980896","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 018 - 018"],"request":{"noSEP":"0904R0080624V017038","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:17:07 {"nomorkartu":"0002143987694","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 018"],"request":{"noSEP":"0904R0080624V018012","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:17:14 {"nomorkartu":"0001725350567","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 018 - 018"],"request":{"noSEP":"0904R0080624V016945","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:17:20 {"nomorkartu":"0001196910178","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 018 - 018"]}
2024-06-20 12:17:27 {"nomorkartu":"0001830576306","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 018 - 018"],"request":{"noSEP":"0904R0080624V011709","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:17:37 {"nomorkartu":"0002363745317","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - 018"],"request":{"noSEP":"0904R0080624V017610","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:17:41 {"nomorkartu":"0001817272923","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 018 - 008"]}
2024-06-20 12:17:49 {"nomorkartu":"0001598928939","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 018"],"request":{"noSEP":"0904R0080624V017586","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:17:54 {"nomorkartu":"0003048031405","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 018 - 021"]}
2024-06-20 12:18:01 {"nomorkartu":"0001800171549","tanggal":"2024-06-20","catatan":["Rujukan RS","15 - 018 - 018"],"request":{"noSEP":"0904R0080624V017652","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:18:08 {"nomorkartu":"0000046838575","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 018 - 018"],"request":{"noSEP":"0904R0080624V017870","kodeDokter":"211860","poliKontrol":"018","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:18:12 {"nomorkartu":"0001719328217","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 018 - OBG"]}
2024-06-20 12:18:16 {"nomorkartu":"0001209764812","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 018 - OBG"]}
2024-06-20 12:18:20 {"nomorkartu":"0000209857702","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - BTK - 008"]}
2024-06-20 12:18:25 {"catatan":["Dokter DPJP di ubah dari 217796 menjadi 298927","Rujukan RS","7 - BTK - BTK"],"nomorkartu":"0001336947276","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V013978","kodeDokter":"298927","poliKontrol":"BTK","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:18:29 {"nomorkartu":"0003305593923","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - BTK - KEM"]}
2024-06-20 12:18:34 {"nomorkartu":"0001807334144","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - BTK - PAR"]}
2024-06-20 12:18:38 {"nomorkartu":"0001733203337","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - BTK - PAR"]}
2024-06-20 12:18:44 {"nomorkartu":"0000022984031","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - BTK - BTK"],"request":{"noSEP":"0904R0080624V017996","kodeDokter":"298927","poliKontrol":"BTK","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:18:48 {"nomorkartu":"0001318594623","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - BTK - PAR"]}
2024-06-20 12:18:55 {"nomorkartu":"0000170889805","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - BTK - BTK"],"request":{"noSEP":"0904R0080624V017364","kodeDokter":"298927","poliKontrol":"BTK","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:19:00 {"nomorkartu":"0000048915483","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - GIZ - 018"]}
2024-06-20 12:19:05 {"nomorkartu":"0002435480908","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - GIZ - URO"]}
2024-06-20 12:19:10 {"nomorkartu":"0001798934411","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - GIZ - 017"]}
2024-06-20 12:19:16 {"nomorkartu":"0000059789665","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - GIZ - 018"]}
2024-06-20 12:19:21 {"nomorkartu":"0001307846968","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - GIZ - 008"]}
2024-06-20 12:19:25 {"nomorkartu":"0002944051075","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - GIZ - 017"]}
2024-06-20 12:19:28 {"nomorkartu":"0002254728486","tanggal":"2024-06-20","catatan":["Rujukan RS","19 - GIZ - THT"]}
2024-06-20 12:19:31 {"nomorkartu":"0000041034216","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - GIZ - 017"]}
2024-06-20 12:19:35 {"nomorkartu":"0002065814673","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - GIZ - 021"]}
2024-06-20 12:19:39 {"nomorkartu":"0000373829365","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - IGD - 017"]}
2024-06-20 12:19:46 {"nomorkartu":"0001081554985","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - IGD - KEM"]}
2024-06-20 12:19:51 {"nomorkartu":"0000041041541","tanggal":"2024-06-20","catatan":["Rujukan RS","22 - IGD - 018"]}
2024-06-20 12:19:55 {"nomorkartu":"0003008113918","tanggal":"2024-06-20","catatan":["Rujukan RS","19 - IGD - SAR"]}
2024-06-20 12:20:09 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan PCARE"," - PAR - INT"],"nomorkartu":"0001474732809","tanggal":"2024-06-20"}
2024-06-20 12:20:14 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","6 - PAR - 021"],"nomorkartu":"0002338010818","tanggal":"2024-06-20"}
2024-06-20 12:20:21 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","4 - PAR - PAR"],"nomorkartu":"0002516722659","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017821","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:20:26 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","1 - PAR - 008"],"nomorkartu":"0002293128167","tanggal":"2024-06-20"}
2024-06-20 12:20:31 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","3 - PAR - 017"],"nomorkartu":"0001370187235","tanggal":"2024-06-20"}
2024-06-20 12:20:39 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","2 - PAR - PAR"],"nomorkartu":"0001718640077","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017321","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:20:45 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","9 - PAR - PAR"],"nomorkartu":"0001565322952","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017506","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:20:49 {"catatan":["Dokter DPJP di ubah dari 284469 menjadi 217523","Rujukan RS","6 - PAR - 008"],"nomorkartu":"0000154818189","tanggal":"2024-06-20"}
2024-06-20 12:20:55 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","11 - PAR - 017"],"nomorkartu":"0001284429273","tanggal":"2024-06-20"}
2024-06-20 12:21:00 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","4 - PAR - BSY"],"nomorkartu":"0001151037731","tanggal":"2024-06-20"}
2024-06-20 12:21:07 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","5 - PAR - PAR"],"nomorkartu":"0000022536369","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017347","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:21:13 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan PCARE"," - PAR - KEM"],"nomorkartu":"0000063402884","tanggal":"2024-06-20"}
2024-06-20 12:21:17 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","10 - PAR - 008"],"nomorkartu":"0002435335097","tanggal":"2024-06-20"}
2024-06-20 12:21:20 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","19 - PAR - 017"],"nomorkartu":"0001725459546","tanggal":"2024-06-20"}
2024-06-20 12:21:25 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan PCARE"," - PAR - 017"],"nomorkartu":"0001309623614","tanggal":"2024-06-20"}
2024-06-20 12:21:28 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","1 - PAR - 008"],"nomorkartu":"0001635546824","tanggal":"2024-06-20"}
2024-06-20 12:21:32 {"catatan":["Dokter DPJP di ubah dari 216602 menjadi 217523","Rujukan RS","9 - PAR - 017"],"nomorkartu":"0001641764913","tanggal":"2024-06-20"}
2024-06-20 12:21:38 {"catatan":["Dokter DPJP di ubah dari 284469 menjadi 217523","Rujukan RS","16 - PAR - PAR"],"nomorkartu":"0002519337453","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016972","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:21:48 {"catatan":["Dokter DPJP di ubah dari 216602 menjadi 217523","Rujukan RS","2 - PAR - PAR"],"nomorkartu":"0002507097262","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V010925","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:21:53 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","7 - PAR - 017"],"nomorkartu":"0000040287071","tanggal":"2024-06-20"}
2024-06-20 12:21:58 {"catatan":["Dokter DPJP di ubah dari 284469 menjadi 217523","Rujukan RS","8 - PAR - URO"],"nomorkartu":"0001222889207","tanggal":"2024-06-20"}
2024-06-20 12:22:05 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","3 - PAR - SAR"],"nomorkartu":"0001079654128","tanggal":"2024-06-20"}
2024-06-20 12:22:09 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","11 - PAR - 004"],"nomorkartu":"0002294105758","tanggal":"2024-06-20"}
2024-06-20 12:22:12 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","6 - PAR - 017"],"nomorkartu":"0002361140886","tanggal":"2024-06-20"}
2024-06-20 12:22:16 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","2 - PAR - PAR"],"nomorkartu":"0001258475365","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017476","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:22:20 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","8 - PAR - 017"],"nomorkartu":"0001319895156","tanggal":"2024-06-20"}
2024-06-20 12:22:25 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","4 - PAR - 017"],"nomorkartu":"0002508027974","tanggal":"2024-06-20"}
2024-06-20 12:22:30 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","10 - PAR - THT"],"nomorkartu":"0002101472987","tanggal":"2024-06-20"}
2024-06-20 12:22:34 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan PCARE"," - PAR - 017"],"nomorkartu":"0001677337986","tanggal":"2024-06-20"}
2024-06-20 12:22:42 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","3 - PAR - PAR"],"nomorkartu":"0000036706948","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V020422","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:22:48 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","8 - PAR - PAR"],"nomorkartu":"0003559301537","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018231","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:22:52 {"catatan":["Dokter DPJP di ubah dari 284469 menjadi 217523","Rujukan RS","5 - PAR - OBG"],"nomorkartu":"0002264667265","tanggal":"2024-06-20"}
2024-06-20 12:22:54 {"catatan":["Dokter DPJP di ubah dari 284469 menjadi 217523","Rujukan RS","23 - PAR - SAR"],"nomorkartu":"0000815220652","tanggal":"2024-06-20"}
2024-06-20 12:23:00 {"catatan":["Dokter DPJP di ubah dari 218332 menjadi 217523","Rujukan RS","1 - PAR - PAR"],"nomorkartu":"0001624568455","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017795","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:23:07 {"catatan":["Dokter DPJP di ubah dari 217629 menjadi 217523","Rujukan RS","8 - PAR - PAR"],"nomorkartu":"0001720837023","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017574","kodeDokter":"217523","poliKontrol":"PAR","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:23:13 {"catatan":["Dokter DPJP di ubah dari 217510 menjadi 216980","Rujukan RS","4 - IRM - 008"],"nomorkartu":"0000977059945","tanggal":"2024-06-20"}
2024-06-20 12:23:18 {"catatan":["Dokter DPJP di ubah dari 217510 menjadi 216980","Rujukan RS","2 - IRM - PAR"],"nomorkartu":"0001213837132","tanggal":"2024-06-20"}
2024-06-20 12:23:28 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0001327231293","tanggal":"2024-06-20"}
2024-06-20 12:23:33 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","7 - IRM - 017"],"nomorkartu":"0001424928137","tanggal":"2024-06-20"}
2024-06-20 12:23:37 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","2 - IRM - 017"],"nomorkartu":"0001327595455","tanggal":"2024-06-20"}
2024-06-20 12:23:39 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","7 - IRM - 017"],"nomorkartu":"0001187553835","tanggal":"2024-06-20"}
2024-06-20 12:23:42 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","11 - IRM - 017"],"nomorkartu":"0001142137631","tanggal":"2024-06-20"}
2024-06-20 12:23:45 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","14 - IRM - 017"],"nomorkartu":"0001794194627","tanggal":"2024-06-20"}
2024-06-20 12:23:48 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","3 - IRM - THT"],"nomorkartu":"0001339023947","tanggal":"2024-06-20"}
2024-06-20 12:23:51 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","19 - IRM - 030"],"nomorkartu":"0002614507604","tanggal":"2024-06-20"}
2024-06-20 12:23:54 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0001317164916","tanggal":"2024-06-20"}
2024-06-20 12:23:58 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","5 - IRM - 017"],"nomorkartu":"0001262076322","tanggal":"2024-06-20"}
2024-06-20 12:24:02 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0000059583497","tanggal":"2024-06-20"}
2024-06-20 12:24:06 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","29 - IRM - 017"],"nomorkartu":"0001741614208","tanggal":"2024-06-20"}
2024-06-20 12:24:09 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","7 - IRM - 017"],"nomorkartu":"0001280392659","tanggal":"2024-06-20"}
2024-06-20 12:24:13 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0000043738841","tanggal":"2024-06-20"}
2024-06-20 12:24:18 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","1 - IRM - 017"],"nomorkartu":"0001288865529","tanggal":"2024-06-20"}
2024-06-20 12:24:27 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","7 - IRM - THT"],"nomorkartu":"0001775611214","tanggal":"2024-06-20"}
2024-06-20 12:24:32 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","13 - IRM - 017"],"nomorkartu":"0000058727349","tanggal":"2024-06-20"}
2024-06-20 12:24:36 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","11 - IRM - THT"],"nomorkartu":"0001271220625","tanggal":"2024-06-20"}
2024-06-20 12:24:40 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","5 - IRM - 017"],"nomorkartu":"0001726681487","tanggal":"2024-06-20"}
2024-06-20 12:24:45 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","2 - IRM - 017"],"nomorkartu":"0001318674126","tanggal":"2024-06-20"}
2024-06-20 12:24:49 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","6 - IRM - 017"],"nomorkartu":"0001335480726","tanggal":"2024-06-20"}
2024-06-20 12:24:55 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0000467654624","tanggal":"2024-06-20"}
2024-06-20 12:25:01 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0001288980595","tanggal":"2024-06-20"}
2024-06-20 12:25:05 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","16 - IRM - 017"],"nomorkartu":"0001800831194","tanggal":"2024-06-20"}
2024-06-20 12:25:10 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0001547189965","tanggal":"2024-06-20"}
2024-06-20 12:25:13 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","9 - IRM - BSY"],"nomorkartu":"0002305963484","tanggal":"2024-06-20"}
2024-06-20 12:25:17 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","1 - IRM - 017"],"nomorkartu":"0002337742978","tanggal":"2024-06-20"}
2024-06-20 12:25:21 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","11 - IRM - 017"],"nomorkartu":"0001215714172","tanggal":"2024-06-20"}
2024-06-20 12:25:26 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - OBG"],"nomorkartu":"0001422021947","tanggal":"2024-06-20"}
2024-06-20 12:25:32 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","1 - IRM - 021"],"nomorkartu":"0000059861935","tanggal":"2024-06-20"}
2024-06-20 12:25:36 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","0 - IRM - THT"],"nomorkartu":"0001732368824","tanggal":"2024-06-20"}
2024-06-20 12:25:39 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","2 - IRM - 017"],"nomorkartu":"0003301396108","tanggal":"2024-06-20"}
2024-06-20 12:25:43 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 008"],"nomorkartu":"0001726030269","tanggal":"2024-06-20"}
2024-06-20 12:25:46 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","7 - IRM - 017"],"nomorkartu":"0001845014354","tanggal":"2024-06-20"}
2024-06-20 12:25:51 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0001766304101","tanggal":"2024-06-20"}
2024-06-20 12:25:54 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","8 - IRM - 017"],"nomorkartu":"0001452166762","tanggal":"2024-06-20"}
2024-06-20 12:25:58 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","19 - IRM - 017"],"nomorkartu":"0002466364061","tanggal":"2024-06-20"}
2024-06-20 12:26:02 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","10 - IRM - 021"],"nomorkartu":"0003565039735","tanggal":"2024-06-20"}
2024-06-20 12:26:10 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","6 - IRM - 017"],"nomorkartu":"0001259972796","tanggal":"2024-06-20"}
2024-06-20 12:26:22 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","10 - IRM - BSY"],"nomorkartu":"0001720985196","tanggal":"2024-06-20"}
2024-06-20 12:26:26 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0002098823051","tanggal":"2024-06-20"}
2024-06-20 12:26:30 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 021"],"nomorkartu":"0000047862213","tanggal":"2024-06-20"}
2024-06-20 12:26:34 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","20 - IRM - 017"],"nomorkartu":"0003140770498","tanggal":"2024-06-20"}
2024-06-20 12:26:38 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","8 - IRM - 017"],"nomorkartu":"0001848000655","tanggal":"2024-06-20"}
2024-06-20 12:26:43 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0001658228343","tanggal":"2024-06-20"}
2024-06-20 12:26:48 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","12 - IRM - 017"],"nomorkartu":"0001646310813","tanggal":"2024-06-20"}
2024-06-20 12:26:55 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan PCARE"," - IRM - 017"],"nomorkartu":"0003202818006","tanggal":"2024-06-20"}
2024-06-20 12:26:59 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","10 - IRM - 017"],"nomorkartu":"0000041160622","tanggal":"2024-06-20"}
2024-06-20 12:27:02 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","5 - IRM - 017"],"nomorkartu":"0000058702724","tanggal":"2024-06-20"}
2024-06-20 12:27:06 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0001245757432","tanggal":"2024-06-20"}
2024-06-20 12:27:09 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","3 - IRM - 008"],"nomorkartu":"0001469500391","tanggal":"2024-06-20"}
2024-06-20 12:27:13 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","10 - IRM - 017"],"nomorkartu":"0001210274076","tanggal":"2024-06-20"}
2024-06-20 12:27:16 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","5 - IRM - 017"],"nomorkartu":"0001810193387","tanggal":"2024-06-20"}
2024-06-20 12:27:19 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","0 - IRM - 018"],"nomorkartu":"0000205087307","tanggal":"2024-06-20"}
2024-06-20 12:27:22 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","2 - IRM - 017"],"nomorkartu":"0002104572396","tanggal":"2024-06-20"}
2024-06-20 12:27:26 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","4 - IRM - 017"],"nomorkartu":"0001725213802","tanggal":"2024-06-20"}
2024-06-20 12:27:33 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","0 - IRM - BSY"],"nomorkartu":"0002487891958","tanggal":"2024-06-20"}
2024-06-20 12:27:39 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","14 - IRM - PAR"],"nomorkartu":"0001338732505","tanggal":"2024-06-20"}
2024-06-20 12:27:42 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","3 - IRM - 017"],"nomorkartu":"0001887990985","tanggal":"2024-06-20"}
2024-06-20 12:27:45 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","16 - IRM - 017"],"nomorkartu":"0001471117869","tanggal":"2024-06-20"}
2024-06-20 12:27:48 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","18 - IRM - INT"],"nomorkartu":"0002355013956","tanggal":"2024-06-20"}
2024-06-20 12:27:51 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","9 - IRM - 017"],"nomorkartu":"0001901957117","tanggal":"2024-06-20"}
2024-06-20 12:27:55 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","5 - IRM - 021"],"nomorkartu":"0001379697535","tanggal":"2024-06-20"}
2024-06-20 12:27:59 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","3 - IRM - 017"],"nomorkartu":"0002100980362","tanggal":"2024-06-20"}
2024-06-20 12:28:07 {"catatan":["Dokter DPJP di ubah dari 217510 menjadi 216980","Rujukan RS","4 - IRM - THT"],"nomorkartu":"0002072769287","tanggal":"2024-06-20"}
2024-06-20 12:28:10 {"catatan":["Dokter DPJP di ubah dari 217510 menjadi 216980","Rujukan RS","5 - IRM - 017"],"nomorkartu":"0001655937156","tanggal":"2024-06-20"}
2024-06-20 12:28:14 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","2 - IRM - 017"],"nomorkartu":"0001434096584","tanggal":"2024-06-20"}
2024-06-20 12:28:20 {"catatan":["Dokter DPJP di ubah dari 217510 menjadi 216980","Rujukan RS","2 - IRM - IRM"],"nomorkartu":"0001790339578","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V004207","kodeDokter":"216980","poliKontrol":"IRM","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:28:25 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","8 - IRM - 017"],"nomorkartu":"0000159512725","tanggal":"2024-06-20"}
2024-06-20 12:28:29 {"catatan":["Dokter DPJP di ubah dari 217881 menjadi 216980","Rujukan RS","8 - IRM - 017"],"nomorkartu":"0001144481409","tanggal":"2024-06-20"}
2024-06-20 12:28:36 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002212331501","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017613","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:28:40 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000047168741","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V006043","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:28:43 {"nomorkartu":"0001259759733","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - 017 - 008"]}
2024-06-20 12:28:49 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000039367743","tanggal":"2024-06-20"}
2024-06-20 12:28:57 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001289211309","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V014162","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:29:06 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001634635708","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016914","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:13 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001766334352","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017739","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000135997558","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017737","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:25 {"nomorkartu":"0001971973765","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 017 - 017"],"request":{"noSEP":"0904R0080624V017204","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:41 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001470088855","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017125","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:44 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 008"],"nomorkartu":"0001278606609","tanggal":"2024-06-20"}
2024-06-20 12:29:49 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001624331114","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017244","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:54 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000036410951","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016789","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:29:59 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002971878401","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017995","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:06 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000047310153","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018215","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:13 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001653768922","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016931","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:18 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002267631718","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018217","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:28 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001125625094","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018028","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:35 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001722691348","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017304","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:42 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0002083532308","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018226","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:48 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001314118901","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017970","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:53 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000173449372","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017269","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:30:58 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0003102282617","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017227","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:03 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000815858943","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018022","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:11 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001597874591","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018259","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:17 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001386615576","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018001","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","0 - 017 - 017"],"nomorkartu":"0000036782471","tanggal":"2024-06-20"}
2024-06-20 12:31:26 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003133118766","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017326","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:31 {"nomorkartu":"0001591233107","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 017 - 017"],"request":{"noSEP":"0904R0080624V018145","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:37 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001259485975","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017719","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:43 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001609562924","tanggal":"2024-06-20"}
2024-06-20 12:31:50 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001205800222","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017032","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:31:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001295193554","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016779","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:11 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001404629471","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017878","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:18 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003528914152","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017392","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:23 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0001626343784","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017277","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:29 {"nomorkartu":"0000039347537","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 017 - 017"],"request":{"noSEP":"0904R0080624V017572","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:39 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001607606717","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017815","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:32:45 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000073224538","tanggal":"2024-06-20"}
2024-06-20 12:32:56 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001321293519","tanggal":"2024-06-20"}
2024-06-20 12:33:09 {"nomorkartu":"0001142619085","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 017 - 017"],"request":{"noSEP":"0904R0080624V017879","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:33:14 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002468428694","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018192","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:33:22 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003506626607","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017536","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:33:26 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001739693799","tanggal":"2024-06-20"}
2024-06-20 12:33:31 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001869967091","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017402","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:33:35 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001298954733","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017274","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:33:39 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0001222984967","tanggal":"2024-06-20"}
2024-06-20 12:33:44 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001625251566","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V020376","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:33:47 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - THT"],"nomorkartu":"0001087048967","tanggal":"2024-06-20"}
2024-06-20 12:33:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0000814594274","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016971","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:00 {"nomorkartu":"0001479720813","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 017 - 017"],"request":{"noSEP":"0904R0080624V018082","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:04 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","8 - 017 - 017"],"nomorkartu":"0002362326063","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017557","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:14 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002103505086","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016918","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:19 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001718278885","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016771","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:23 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000048861044","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018122","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:31 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000046767778","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017792","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:37 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001297105569","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017354","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:41 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001590175754","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017357","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:45 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001452150854","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V013699","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:34:50 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000155132425","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017463","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:34:53 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001257423085","tanggal":"2024-06-20"}
2024-06-20 12:35:02 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001266229721","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017543","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:35:08 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001097476255","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017716","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:35:16 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001654902562","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017874","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:35:21 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001717720222","tanggal":"2024-06-20"}
2024-06-20 12:35:24 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","6 - 017 - THT"],"nomorkartu":"0001310099883","tanggal":"2024-06-20"}
2024-06-20 12:35:28 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000031618451","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017593","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:35:39 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000070449581","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V014364","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:35:44 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000513031869","tanggal":"2024-06-20"}
2024-06-20 12:35:50 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000012856061","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017627","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:00 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001259121251","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017015","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:06 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001629146406","tanggal":"2024-06-20"}
2024-06-20 12:36:08 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","0 - 017 - 017"],"nomorkartu":"0002885713683","tanggal":"2024-06-20"}
2024-06-20 12:36:14 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001460874699","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017108","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001638860758","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016874","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:27 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000816098231","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017386","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:37 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001427245086","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017587","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:41 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000810427678","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017725","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:45 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001592115816","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017001","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:49 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002294631448","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017727","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:53 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001478818776","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018006","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:36:57 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","26 - 017 - 017"],"nomorkartu":"0001803868615","tanggal":"2024-06-20","request":{"noSEP":null,"kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"401","message":"Transaksi tidak dapat diproses"},"response":null}}
2024-06-20 12:37:01 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000815735935","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017151","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:08 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001377862266","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017154","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:11 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000817823946","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017411","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:15 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001733127851","tanggal":"2024-06-20"}
2024-06-20 12:37:20 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001872736817","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017127","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:24 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001611203635","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V014213","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:37:27 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001303668022","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016902","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:30 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001369496439","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018210","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:34 {"nomorkartu":"0001102745439","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 017 - 017"],"request":{"noSEP":"0904R0080624V017892","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:36 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0001769667366","tanggal":"2024-06-20"}
2024-06-20 12:37:41 {"nomorkartu":"0001457880873","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 017 - 017"]}
2024-06-20 12:37:46 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002629641576","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017560","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:51 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001767604173","tanggal":"2024-06-20"}
2024-06-20 12:37:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001788493601","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016794","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:37:58 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000047404708","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017829","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:05 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001456048721","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018004","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:09 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001220138728","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017210","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:12 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002039968901","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017540","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:16 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001338135917","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V005339","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:38:19 {"nomorkartu":"0002940728591","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 017 - 017"],"request":{"noSEP":"0904R0080624V017378","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:23 {"nomorkartu":"0001812277113","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 017 - 017"],"request":{"noSEP":"0904R0080624V017845","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:27 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001720217068","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017460","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:32 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001128287362","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016948","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:40 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","26 - 017 - 017"],"nomorkartu":"0002049317897","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018206","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:46 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001210193019","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017835","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:51 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001440245654","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017588","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:38:56 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001132148507","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016786","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:00 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000043495413","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017515","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:04 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001221762532","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017880","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:09 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0003174069339","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016940","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001338735587","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018075","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:32 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000148131808","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016911","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:37 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0002927470612","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017284","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:42 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0001606178856","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017862","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:39:53 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002966831155","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V020027","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:40:05 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000043127133","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018019","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:40:10 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002188648102","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016986","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:40:15 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001339006792","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V015189","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:40:24 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001718840013","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016982","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:40:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","0 - 017 - 017"],"nomorkartu":"0001441425677","tanggal":"2024-06-20"}
2024-06-20 12:40:34 {"nomorkartu":"0002511307034","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 017 - 017"],"request":{"noSEP":"0904R0080624V018013","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:40:54 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","12 - 017 - 017"],"nomorkartu":"0003555414876","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V003747","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:40:59 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002169776215","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018163","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:05 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002044884003","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017257","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:10 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0003343305497","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016795","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:14 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001211602948","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017214","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0000048998891","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V026241","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:41:27 {"catatan":["Dokter DPJP di ubah dari 216934 menjadi 217790","Rujukan RS","8 - 017 - 017"],"nomorkartu":"0002469659591","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017886","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:32 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001456303285","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017646","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:40 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001600732438","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016930","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:45 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001670746353","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017309","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:41:53 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002099623329","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V005819","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:41:56 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0002422883272","tanggal":"2024-06-20"}
2024-06-20 12:41:59 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0002082691629","tanggal":"2024-06-20"}
2024-06-20 12:42:03 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001704373018","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017770","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:08 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","8 - 017 - 017"],"nomorkartu":"0000043091627","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017677","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:12 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002082070282","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V005138","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:42:16 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001733999499","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V001935","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:42:20 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000194760797","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V002048","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:42:25 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","12 - 017 - 017"],"nomorkartu":"0002342231256","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016834","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:28 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003543959507","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017230","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:31 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001293853555","tanggal":"2024-06-20"}
2024-06-20 12:42:34 {"catatan":["Dokter DPJP di ubah dari 216934 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002763985691","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007715","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:42:42 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001848649329","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018268","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:49 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002507124126","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016778","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:42:55 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000010804768","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017889","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:01 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0002052763288","tanggal":"2024-06-20"}
2024-06-20 12:43:06 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001877877268","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017168","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:10 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001892940041","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018221","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:14 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","13 - 017 - 017"],"nomorkartu":"0001209566463","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018239","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:18 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001654893483","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016934","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:21 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","12 - 017 - 017"],"nomorkartu":"0001260329038","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017122","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:24 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0000794420752","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017183","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","15 - 017 - 017"],"nomorkartu":"0002337688495","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017324","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:31 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","14 - 017 - 017"],"nomorkartu":"0001580521342","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017601","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:35 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","14 - 017 - 017"],"nomorkartu":"0001788332332","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017343","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:38 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0001256648692","tanggal":"2024-06-20"}
2024-06-20 12:43:42 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002876618259","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017443","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:45 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0002332165869","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017136","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:43:57 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000063733149","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017276","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:03 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","25 - 017 - 008"],"nomorkartu":"0000038660635","tanggal":"2024-06-20"}
2024-06-20 12:44:13 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001334477125","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017454","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:17 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001193495837","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017825","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:20 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001901225294","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016952","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:24 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002778807554","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017675","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:27 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 008"],"nomorkartu":"0001156827104","tanggal":"2024-06-20"}
2024-06-20 12:44:43 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001310076832","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007861","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:44:46 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001220709892","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016921","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:50 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002393353967","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017260","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:53 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001600217831","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016910","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:44:56 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","16 - 017 - 017"],"nomorkartu":"0000043132656","tanggal":"2024-06-20","request":{"noSEP":null,"kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"401","message":"Transaksi tidak dapat diproses"},"response":null}}
2024-06-20 12:44:58 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000037650374","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017865","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:02 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001729177817","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V008540","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:45:07 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001371750816","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018005","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:10 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0000045154091","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017807","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:13 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001609063828","tanggal":"2024-06-20"}
2024-06-20 12:45:17 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001441574324","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016907","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:21 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000042340871","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V021500","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Nomor SEP Sudah pernah digunakan!."},"response":null}}
2024-06-20 12:45:25 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0003137642932","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018032","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:27 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001271408692","tanggal":"2024-06-20"}
2024-06-20 12:45:30 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","0 - 017 - 017"],"nomorkartu":"0000067080947","tanggal":"2024-06-20"}
2024-06-20 12:45:33 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0002102654338","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016997","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:38 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000047738417","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V014508","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006393","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0000047738417","nama":"NURYANINGSIH","kelamin":"Perempuan","tglLahir":"1973-07-07","namaDiagnosa":"D24 - Benign neoplasm of breast"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V014508', '217790', '017', '0904R0080624K006393', '0000047738417', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'NURYANINGSIH (ADI SUMARNO)', '1065')<br>MySQL server has gone away"}
2024-06-20 12:45:44 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001459999462","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018190","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:48 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","17 - 017 - 017"],"nomorkartu":"0001813137759","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018236","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:51 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","8 - 017 - 017"],"nomorkartu":"0002237999973","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017585","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:54 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","14 - 017 - 017"],"nomorkartu":"0001473858911","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016933","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:45:57 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001222521243","tanggal":"2024-06-20"}
2024-06-20 12:46:01 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","16 - 017 - 017"],"nomorkartu":"0002441458787","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017120","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:02 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","13 - 017 - 008"],"nomorkartu":"0001474232736","tanggal":"2024-06-20"}
2024-06-20 12:46:06 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","11 - 017 - 017"],"nomorkartu":"0000154496619","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017467","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:09 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0002336088723","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017028","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:12 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","13 - 017 - 017"],"nomorkartu":"0001472875053","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017253","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:14 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000001020273","tanggal":"2024-06-20"}
2024-06-20 12:46:17 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0002884113303","tanggal":"2024-06-20"}
2024-06-20 12:46:20 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001483823979","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016770","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:23 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","13 - 017 - 017"],"nomorkartu":"0003162111423","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017924","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:25 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0002749460152","tanggal":"2024-06-20"}
2024-06-20 12:46:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0002032364204","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016885","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:31 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001377662714","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V013950","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:46:33 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000818632271","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018265","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:36 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000480108251","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017205","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:38 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000377093992","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017504","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:41 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001278289405","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016892","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:44 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001720042874","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017149","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:47 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001256717722","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016825","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:50 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002738955734","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017447","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:52 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0000037772256","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017092","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:54 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0002243428402","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018198","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001875040312","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017965","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:57 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","13 - 017 - 017"],"nomorkartu":"0000030965422","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017311","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:46:59 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0000109844324","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007178","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:47:02 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0002308352207","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018023","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:05 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001221679675","tanggal":"2024-06-20"}
2024-06-20 12:47:08 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001454262221","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018254","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:10 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001277303758","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V010376","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:47:12 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0000046745706","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007761","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:47:14 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001430251525","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017005","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:16 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002882762381","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017416","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:19 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000817321601","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016884","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:23 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","16 - 017 - 017"],"nomorkartu":"0002243086468","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017833","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:26 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","15 - 017 - 017"],"nomorkartu":"0001620829168","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016781","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001652577985","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017558","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:30 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0002137413295","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017129","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:32 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001338753047","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016959","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:33 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 021"],"nomorkartu":"0001315444779","tanggal":"2024-06-20"}
2024-06-20 12:47:34 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001807353551","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016916","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:35 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","0 - 017 - 017"],"nomorkartu":"0002294647154","tanggal":"2024-06-20"}
2024-06-20 12:47:39 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002507407301","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017781","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:40 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 018"],"nomorkartu":"0002931245583","tanggal":"2024-06-20"}
2024-06-20 12:47:43 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001455475061","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018158","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:46 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001658432801","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017456","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:49 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0002279811363","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V021664","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:47:51 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001297018664","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017656","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0000038853639","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017666","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:47:58 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001214209899","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018043","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:01 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001740599616","tanggal":"2024-06-20"}
2024-06-20 12:48:08 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000043706204","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017937","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:09 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001305045189","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V012242","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:48:13 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001209769132","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017478","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:31 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS"," - 017 - 017"],"nomorkartu":"0001258530726","tanggal":"2024-06-20"}
2024-06-20 12:48:38 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 008"],"nomorkartu":"0002646965733","tanggal":"2024-06-20"}
2024-06-20 12:48:41 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002362030266","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018116","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:44 {"catatan":["Dokter DPJP di ubah dari 216934 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000037199632","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V003546","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006395","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0000037199632","nama":"LENNY MARDIATI","kelamin":"Perempuan","tglLahir":"1953-06-06","namaDiagnosa":"C50.9 - Malignant neoplasm of breast, unspecified"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V003546', '217790', '017', '0904R0080624K006395', '0000037199632', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'LENNY MARDIATI, NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:48:47 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001732719339","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017066","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:49 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002048185888","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017087","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:51 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001011091149","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016888","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:52 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0003256286016","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V014242","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006396","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0003256286016","nama":"ODAH","kelamin":"Perempuan","tglLahir":"1969-08-12","namaDiagnosa":"C50 - Malignant neoplasm of breast"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V014242', '217790', '017', '0904R0080624K006396', '0003256286016', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'ODAH, NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:48:54 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000795950267","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017635","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:48:57 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","29 - 017 - 017"],"nomorkartu":"0001204852296","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007144","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006397","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0001204852296","nama":"INDAH APRILIYANI","kelamin":"Perempuan","tglLahir":"1983-04-05","namaDiagnosa":"Z09.8 - Follow-up examination after other treatment for other conditions"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V007144', '217790', '017', '0904R0080624K006397', '0001204852296', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'INDAH APRILIYANI, NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:48:58 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001849917688","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016792","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:00 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0000040881587","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017054","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:03 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002104023802","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017195","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:04 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000041695492","tanggal":"2024-06-20"}
2024-06-20 12:49:06 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001617277149","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017429","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:09 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001892327815","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007315","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006398","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0001892327815","nama":"SINTA","kelamin":"Perempuan","tglLahir":"1981-08-06","namaDiagnosa":"D34 - Benign neoplasm of thyroid gland"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V007315', '217790', '017', '0904R0080624K006398', '0001892327815', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'SINTA A.MD (H.SANUSI), NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:49:14 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001633731838","tanggal":"2024-06-20"}
2024-06-20 12:49:19 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0000041785457","tanggal":"2024-06-20"}
2024-06-20 12:49:21 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001738798492","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016772","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:23 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","22 - 017 - 017"],"nomorkartu":"0000368088862","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016994","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:25 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0002329552214","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018034","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:26 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0002424880822","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017306","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001814182323","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017696","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:30 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0002802316004","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017891","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:33 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000036241615","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017621","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:35 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","8 - 017 - 017"],"nomorkartu":"0002272648239","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017461","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:36 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0003252061811","tanggal":"2024-06-20"}
2024-06-20 12:49:39 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002622592168","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017245","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:42 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000040357034","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V013219","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:49:46 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001427348823","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V026831","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006400","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0001427348823","nama":"LELIANI","kelamin":"Perempuan","tglLahir":"1979-10-02","namaDiagnosa":"D24 - Benign neoplasm of breast"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080524V026831', '217790', '017', '0904R0080624K006400', '0001427348823', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'LELIANI', '1065')<br>MySQL server has gone away"}
2024-06-20 12:49:48 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0001767097078","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018132","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:50 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000049357697","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017789","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:51 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 021"],"nomorkartu":"0001260231748","tanggal":"2024-06-20"}
2024-06-20 12:49:53 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001314212354","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016889","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:55 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001228173726","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016861","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:49:58 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001826536239","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V014885","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:50:02 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001407834325","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017289","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:06 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001455255865","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018110","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:10 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001337080465","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018061","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:13 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002076843734","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017158","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:16 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0000042771655","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016797","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:19 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001649325407","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017642","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:23 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0001793851705","tanggal":"2024-06-20"}
2024-06-20 12:50:26 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001293368545","tanggal":"2024-06-20"}
2024-06-20 12:50:28 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000037318678","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017041","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:30 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","9 - 017 - 017"],"nomorkartu":"0001438373103","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017048","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:32 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0002294646579","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V007390","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006401","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0002294646579","nama":"DESI RATNASARI","kelamin":"Perempuan","tglLahir":"1994-07-06","namaDiagnosa":"C73 - Malignant neoplasm of thyroid gland"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V007390', '217790', '017', '0904R0080624K006401', '0002294646579', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'DESI RATNASARI', '1065')<br>MySQL server has gone away"}
2024-06-20 12:50:34 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","25 - 017 - 017"],"nomorkartu":"0000049376755","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018067","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:37 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001441785554","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018009","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:39 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001787122416","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017179","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:40 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 008"],"nomorkartu":"0003287068064","tanggal":"2024-06-20"}
2024-06-20 12:50:44 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","18 - 017 - 017"],"nomorkartu":"0001768025711","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018125","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:46 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001652512386","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018262","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:48 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001722095346","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017029","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:54 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","10 - 017 - 017"],"nomorkartu":"0002738319647","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017688","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:56 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0000079718613","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016987","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:50:58 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0001441556098","tanggal":"2024-06-20"}
2024-06-20 12:51:01 {"nomorkartu":"0002143662489","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 017 - 017"],"request":{"noSEP":"0904R0080624V017166","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:03 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001275181378","tanggal":"2024-06-20"}
2024-06-20 12:51:06 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003052572197","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017000","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:09 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0002041204162","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017939","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:12 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0000488082791","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016819","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:16 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001168882593","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017186","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:16 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","18 - 017 - 008"],"nomorkartu":"0000374942373","tanggal":"2024-06-20"}
2024-06-20 12:51:20 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001370284986","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017471","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:21 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001168502365","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018017","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:23 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0000031596039","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017060","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:24 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan PCARE"," - 017 - 017"],"nomorkartu":"0001741541602","tanggal":"2024-06-20"}
2024-06-20 12:51:25 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001876326682","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016658","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006402","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0001876326682","nama":"PUTRI FURQONI AMALIA KHAMARANI","kelamin":"Perempuan","tglLahir":"1994-06-18","namaDiagnosa":"C73 - Malignant neoplasm of thyroid gland"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V016658', '217790', '017', '0904R0080624K006402', '0001876326682', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'PUTRI FURQONI AMALIA KHAMARANI, NN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:51:26 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001441782999","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017940","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:28 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001643801883","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017751","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:29 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","3 - 017 - 017"],"nomorkartu":"0001506266515","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018174","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:30 {"nomorkartu":"0001732475687","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 017 - 017"],"request":{"noSEP":"0904R0080624V017774","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:32 {"nomorkartu":"0002278138241","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 017 - 017"],"request":{"noSEP":"0904R0080624V016961","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:33 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001258338058","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017242","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:34 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001738656189","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018084","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:35 {"catatan":["Dokter DPJP di ubah dari 216934 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001421853658","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017840","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:36 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002207885253","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018059","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:37 {"catatan":["Dokter DPJP di ubah dari 217792 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0003080530743","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017362","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:38 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","24 - 017 - 017"],"nomorkartu":"0001446519756","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016864","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:39 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","6 - 017 - 017"],"nomorkartu":"0001767386665","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016773","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:40 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002359598049","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017436","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:41 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0000072506384","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017177","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:43 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001642703196","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V015665","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006403","tglRencanaKontrol":"2024-06-20","namaDokter":"WALTA GAUTAMA","noKartu":"0001642703196","nama":"TINTIN HUTAGAOL","kelamin":"Perempuan","tglLahir":"1986-12-01","namaDiagnosa":"C50.9 - Malignant neoplasm of breast, unspecified"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V015665', '217790', '017', '0904R0080624K006403', '0001642703196', 'WALTA GAUTAMA, dr., SpB(K)Onk', '2024-06-20', 'TINTIN HUTAGAOL, NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:51:44 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001866392379","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017497","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:44 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","2 - 017 - 017"],"nomorkartu":"0001338987756","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017591","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:46 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","4 - 017 - 017"],"nomorkartu":"0001190613565","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018003","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:46 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","22 - 017 - 017"],"nomorkartu":"0000163382207","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017592","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:48 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0002461142327","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017647","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:49 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002207902386","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017672","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:50 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","7 - 017 - 017"],"nomorkartu":"0001530004814","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018271","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:52 {"catatan":["Dokter DPJP di ubah dari 217791 menjadi 217790","Rujukan RS","12 - 017 - 017"],"nomorkartu":"0000361932142","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017860","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:53 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001717476546","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017749","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:55 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0002338545148","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017745","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:51:59 {"catatan":["Dokter DPJP di ubah dari 263340 menjadi 217790","Rujukan RS","1 - 017 - 017"],"nomorkartu":"0001305538661","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017848","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:01 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan PCARE"," - 017 - KEM"],"nomorkartu":"0000042321971","tanggal":"2024-06-20"}
2024-06-20 12:52:02 {"nomorkartu":"0003150581951","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 017 - 017"],"request":{"noSEP":"0904R0080624V017982","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:03 {"catatan":["Dokter DPJP di ubah dari 218064 menjadi 217790","Rujukan RS","5 - 017 - 017"],"nomorkartu":"0001393159487","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018016","kodeDokter":"217790","poliKontrol":"017","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:05 {"nomorkartu":"0001430474602","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 008"],"request":{"noSEP":"0904R0080624V017185","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:06 {"nomorkartu":"0001858401854","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 021"]}
2024-06-20 12:52:07 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - THT"],"nomorkartu":"0001311823089","tanggal":"2024-06-20"}
2024-06-20 12:52:08 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0002302500036","tanggal":"2024-06-20"}
2024-06-20 12:52:09 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 021"],"nomorkartu":"0001274738095","tanggal":"2024-06-20"}
2024-06-20 12:52:10 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0001633280117","tanggal":"2024-06-20"}
2024-06-20 12:52:11 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0002095366948","tanggal":"2024-06-20"}
2024-06-20 12:52:12 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0001632790811","tanggal":"2024-06-20"}
2024-06-20 12:52:13 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","11 - 008 - PAR"],"nomorkartu":"0002354236378","tanggal":"2024-06-20"}
2024-06-20 12:52:13 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - THT"],"nomorkartu":"0000368259129","tanggal":"2024-06-20"}
2024-06-20 12:52:14 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0000039611845","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017626","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:15 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0000043336899","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017069","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:16 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - 008"],"nomorkartu":"0001288352248","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017237","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:17 {"nomorkartu":"0003515422105","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - 008 - THT"]}
2024-06-20 12:52:18 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001129201288","tanggal":"2024-06-20"}
2024-06-20 12:52:19 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0001805423286","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017525","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:20 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","14 - 008 - 008"],"nomorkartu":"0003582805241","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017254","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:20 {"nomorkartu":"0002337327112","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - 008 - PAR"]}
2024-06-20 12:52:21 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","12 - 008 - THT"],"nomorkartu":"0001159128898","tanggal":"2024-06-20"}
2024-06-20 12:52:22 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0001432388722","tanggal":"2024-06-20"}
2024-06-20 12:52:22 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0001314842523","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017026","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:24 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0001482478424","tanggal":"2024-06-20"}
2024-06-20 12:52:24 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001149458275","tanggal":"2024-06-20"}
2024-06-20 12:52:26 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0000041515683","tanggal":"2024-06-20"}
2024-06-20 12:52:27 {"nomorkartu":"0003003974008","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 008 - THT"]}
2024-06-20 12:52:28 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - INT"],"nomorkartu":"0001650891791","tanggal":"2024-06-20"}
2024-06-20 12:52:29 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0002213317113","tanggal":"2024-06-20"}
2024-06-20 12:52:30 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","4 - 008 - INT"],"nomorkartu":"0001208913647","tanggal":"2024-06-20"}
2024-06-20 12:52:31 {"nomorkartu":"0001157006125","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 008"],"request":{"noSEP":"0904R0080624V016965","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:32 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0001592766202","tanggal":"2024-06-20"}
2024-06-20 12:52:33 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0001636017175","tanggal":"2024-06-20"}
2024-06-20 12:52:34 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0002195497506","tanggal":"2024-06-20"}
2024-06-20 12:52:36 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0002581128674","tanggal":"2024-06-20"}
2024-06-20 12:52:37 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - BSY"],"nomorkartu":"0001849616098","tanggal":"2024-06-20"}
2024-06-20 12:52:38 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","0 - 008 - PAR"],"nomorkartu":"0002495670704","tanggal":"2024-06-20"}
2024-06-20 12:52:39 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0002217276371","tanggal":"2024-06-20"}
2024-06-20 12:52:40 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0001080058105","tanggal":"2024-06-20"}
2024-06-20 12:52:41 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0001875464818","tanggal":"2024-06-20"}
2024-06-20 12:52:42 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","9 - 008 - URO"],"nomorkartu":"0001772248803","tanggal":"2024-06-20"}
2024-06-20 12:52:42 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - THT"],"nomorkartu":"0001743003821","tanggal":"2024-06-20"}
2024-06-20 12:52:43 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","0 - 008 - PAR"],"nomorkartu":"0001644678224","tanggal":"2024-06-20"}
2024-06-20 12:52:44 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0002360636763","tanggal":"2024-06-20"}
2024-06-20 12:52:46 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0000041485724","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017641","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:47 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0002473762061","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017146","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:48 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","7 - 008 - INT"],"nomorkartu":"0001731048028","tanggal":"2024-06-20"}
2024-06-20 12:52:49 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - PAR"],"nomorkartu":"0001845336183","tanggal":"2024-06-20"}
2024-06-20 12:52:50 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0000040850111","tanggal":"2024-06-20"}
2024-06-20 12:52:51 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0002251326418","tanggal":"2024-06-20"}
2024-06-20 12:52:52 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0002095341581","tanggal":"2024-06-20"}
2024-06-20 12:52:53 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001466643374","tanggal":"2024-06-20"}
2024-06-20 12:52:53 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - INT"],"nomorkartu":"0000976984132","tanggal":"2024-06-20"}
2024-06-20 12:52:55 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0003340504877","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018164","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:52:56 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","18 - 008 - 017"],"nomorkartu":"0001657610021","tanggal":"2024-06-20"}
2024-06-20 12:52:57 {"nomorkartu":"0001220013382","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - 017"]}
2024-06-20 12:52:58 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","1 - 008 - PAR"],"nomorkartu":"0001524692024","tanggal":"2024-06-20"}
2024-06-20 12:52:59 {"nomorkartu":"0001095200853","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - 008"],"request":{"noSEP":"0904R0080624V000713","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006405","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0001095200853","nama":"MUHAMMAD HAIKAL","kelamin":"Laki-laki","tglLahir":"1980-12-26","namaDiagnosa":"C95.9 - Leukaemia, unspecified"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V000713', '216749', '008', '0904R0080624K006405', '0001095200853', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'MUHAMMAD HAIKAL', '1065')<br>MySQL server has gone away"}
2024-06-20 12:53:00 {"nomorkartu":"0001901884724","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 008"],"request":{"noSEP":"0904R0080624V016974","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:01 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","7 - 008 - 008"],"nomorkartu":"0001265619306","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017004","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:06 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","6 - 008 - 018"],"nomorkartu":"0001427424592","tanggal":"2024-06-20"}
2024-06-20 12:53:07 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0001383842913","tanggal":"2024-06-20"}
2024-06-20 12:53:08 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001092977144","tanggal":"2024-06-20"}
2024-06-20 12:53:09 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001222699577","tanggal":"2024-06-20"}
2024-06-20 12:53:10 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0001637842825","tanggal":"2024-06-20"}
2024-06-20 12:53:11 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0003553238215","tanggal":"2024-06-20"}
2024-06-20 12:53:12 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001434727192","tanggal":"2024-06-20"}
2024-06-20 12:53:13 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0000046355826","tanggal":"2024-06-20"}
2024-06-20 12:53:15 {"nomorkartu":"0000043321217","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 008 - 008"],"request":{"noSEP":"0904R0080624V016960","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:16 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001606115237","tanggal":"2024-06-20"}
2024-06-20 12:53:18 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001375029055","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017014","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:19 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001798829278","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017798","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:20 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0000037681468","tanggal":"2024-06-20"}
2024-06-20 12:53:21 {"nomorkartu":"0003046920581","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 008"],"request":{"noSEP":"0904R0080624V017710","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:22 {"nomorkartu":"0001320833507","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 008"],"request":{"noSEP":"0904R0080624V017307","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:23 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","18 - 008 - 017"],"nomorkartu":"0002337623684","tanggal":"2024-06-20"}
2024-06-20 12:53:24 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001259110517","tanggal":"2024-06-20"}
2024-06-20 12:53:25 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0003105438153","tanggal":"2024-06-20"}
2024-06-20 12:53:31 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001440145113","tanggal":"2024-06-20"}
2024-06-20 12:53:32 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","6 - 008 - 008"],"nomorkartu":"0000055903419","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017300","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:33 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0000373995988","tanggal":"2024-06-20"}
2024-06-20 12:53:34 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001744678776","tanggal":"2024-06-20"}
2024-06-20 12:53:35 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","10 - 008 - INT"],"nomorkartu":"0002361268102","tanggal":"2024-06-20"}
2024-06-20 12:53:35 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","4 - 008 - THT"],"nomorkartu":"0002362724548","tanggal":"2024-06-20"}
2024-06-20 12:53:36 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0001371326714","tanggal":"2024-06-20"}
2024-06-20 12:53:37 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0001303413513","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018014","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:37 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - URO"],"nomorkartu":"0001866334241","tanggal":"2024-06-20"}
2024-06-20 12:53:38 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001482638995","tanggal":"2024-06-20"}
2024-06-20 12:53:39 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0000368125233","tanggal":"2024-06-20"}
2024-06-20 12:53:39 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","9 - 008 - PAR"],"nomorkartu":"0002101721332","tanggal":"2024-06-20"}
2024-06-20 12:53:40 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0000164284479","tanggal":"2024-06-20"}
2024-06-20 12:53:41 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001767601675","tanggal":"2024-06-20"}
2024-06-20 12:53:42 {"nomorkartu":"0002745588137","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 021"]}
2024-06-20 12:53:43 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0002922337361","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017914","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:44 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","10 - 008 - 008"],"nomorkartu":"0001157283527","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017217","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:45 {"nomorkartu":"0002335190117","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 018"]}
2024-06-20 12:53:45 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","1 - 008 - PAR"],"nomorkartu":"0001960096713","tanggal":"2024-06-20"}
2024-06-20 12:53:46 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","6 - 008 - 008"],"nomorkartu":"0002376049904","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016780","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:48 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","9 - 008 - 008"],"nomorkartu":"0000064677734","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017913","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:49 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001423712248","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V023233","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006406","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0001423712248","nama":"MISRIN","kelamin":"Laki-laki","tglLahir":"1975-05-08","namaDiagnosa":"D55.9 - Anaemia due to enzyme disorder, unspecified"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080524V023233', '216749', '008', '0904R0080624K006406', '0001423712248', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'MISRIN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:53:50 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","7 - 008 - ORT"],"nomorkartu":"0000814772474","tanggal":"2024-06-20"}
2024-06-20 12:53:50 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001644783063","tanggal":"2024-06-20"}
2024-06-20 12:53:51 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0003175633809","tanggal":"2024-06-20"}
2024-06-20 12:53:52 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001128527021","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018055","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:54 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0003101192741","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017529","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:54 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","14 - 008 - PAR"],"nomorkartu":"0001649759973","tanggal":"2024-06-20"}
2024-06-20 12:53:56 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","9 - 008 - 008"],"nomorkartu":"0002291048831","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017899","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:56 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","9 - 008 - 008"],"nomorkartu":"0001455381055","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017233","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:53:58 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","0 - 008 - PAR"],"nomorkartu":"0001640761907","tanggal":"2024-06-20"}
2024-06-20 12:53:59 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","14 - 008 - 017"],"nomorkartu":"0002440986669","tanggal":"2024-06-20"}
2024-06-20 12:54:00 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001221323275","tanggal":"2024-06-20"}
2024-06-20 12:54:02 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0001282635674","tanggal":"2024-06-20"}
2024-06-20 12:54:04 {"nomorkartu":"0000049692071","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - 008 - 008"]}
2024-06-20 12:54:04 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0000060052656","tanggal":"2024-06-20"}
2024-06-20 12:54:05 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001464934105","tanggal":"2024-06-20"}
2024-06-20 12:54:06 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","6 - 008 - 018"],"nomorkartu":"0000044921103","tanggal":"2024-06-20"}
2024-06-20 12:54:07 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0000151422884","tanggal":"2024-06-20"}
2024-06-20 12:54:08 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0001336117015","tanggal":"2024-06-20"}
2024-06-20 12:54:09 {"nomorkartu":"0001143283105","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 008 - THT"]}
2024-06-20 12:54:10 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001819414585","tanggal":"2024-06-20"}
2024-06-20 12:54:12 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0001152513011","tanggal":"2024-06-20"}
2024-06-20 12:54:12 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - PAR"],"nomorkartu":"0000208443442","tanggal":"2024-06-20"}
2024-06-20 12:54:13 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","9 - 008 - THT"],"nomorkartu":"0000155118003","tanggal":"2024-06-20"}
2024-06-20 12:54:14 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","7 - 008 - 008"],"nomorkartu":"0000165716482","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017314","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:15 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0003141851973","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017339","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:16 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","15 - 008 - 017"],"nomorkartu":"0003533297253","tanggal":"2024-06-20"}
2024-06-20 12:54:17 {"nomorkartu":"0001773499285","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - THT"]}
2024-06-20 12:54:18 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0002057941203","tanggal":"2024-06-20"}
2024-06-20 12:54:18 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","8 - 008 - THT"],"nomorkartu":"0003578105755","tanggal":"2024-06-20"}
2024-06-20 12:54:19 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","7 - 008 - 018"],"nomorkartu":"0003290321992","tanggal":"2024-06-20"}
2024-06-20 12:54:19 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","4 - 008 - 018"],"nomorkartu":"0002443216779","tanggal":"2024-06-20"}
2024-06-20 12:54:20 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001600695652","tanggal":"2024-06-20"}
2024-06-20 12:54:21 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0001621986895","tanggal":"2024-06-20"}
2024-06-20 12:54:21 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 018"],"nomorkartu":"0003298880619","tanggal":"2024-06-20"}
2024-06-20 12:54:22 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","19 - 008 - 017"],"nomorkartu":"0002797359423","tanggal":"2024-06-20"}
2024-06-20 12:54:22 {"nomorkartu":"0002076937211","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 008 - 021"]}
2024-06-20 12:54:23 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","27 - 008 - 017"],"nomorkartu":"0002943116267","tanggal":"2024-06-20"}
2024-06-20 12:54:24 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0001807250398","tanggal":"2024-06-20"}
2024-06-20 12:54:25 {"nomorkartu":"0001773927516","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - 008 - 021"]}
2024-06-20 12:54:26 {"nomorkartu":"0002473194519","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 008 - 017"]}
2024-06-20 12:54:26 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","15 - 008 - 017"],"nomorkartu":"0001320241329","tanggal":"2024-06-20"}
2024-06-20 12:54:27 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0002748772642","tanggal":"2024-06-20"}
2024-06-20 12:54:28 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","5 - 008 - 018"],"nomorkartu":"0000047833569","tanggal":"2024-06-20"}
2024-06-20 12:54:29 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001659529315","tanggal":"2024-06-20"}
2024-06-20 12:54:30 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0001432423078","tanggal":"2024-06-20"}
2024-06-20 12:54:30 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","19 - 008 - 017"],"nomorkartu":"0002313991157","tanggal":"2024-06-20"}
2024-06-20 12:54:31 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0001799496876","tanggal":"2024-06-20"}
2024-06-20 12:54:32 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0000978470357","tanggal":"2024-06-20"}
2024-06-20 12:54:36 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0001961162796","tanggal":"2024-06-20"}
2024-06-20 12:54:37 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","6 - 008 - THT"],"nomorkartu":"0000460447479","tanggal":"2024-06-20"}
2024-06-20 12:54:39 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 018"],"nomorkartu":"0000044699332","tanggal":"2024-06-20"}
2024-06-20 12:54:40 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0001632739419","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017649","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:41 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","6 - 008 - THT"],"nomorkartu":"0001648540809","tanggal":"2024-06-20"}
2024-06-20 12:54:42 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001740191703","tanggal":"2024-06-20"}
2024-06-20 12:54:43 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","12 - 008 - 008"],"nomorkartu":"0000041809645","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017070","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:43 {"nomorkartu":"0002052295334","tanggal":"2024-06-20","catatan":["Rujukan RS","32 - 008 - THT"]}
2024-06-20 12:54:44 {"nomorkartu":"0002263054588","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - 005"]}
2024-06-20 12:54:44 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0001377175634","tanggal":"2024-06-20"}
2024-06-20 12:54:44 {"nomorkartu":"0001726847414","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - THT"]}
2024-06-20 12:54:45 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001843084247","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V017343","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Status peserta tidak aktif (PENANGGUHAN PESERTA)"},"response":null}}
2024-06-20 12:54:45 {"nomorkartu":"0001448630605","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 008"],"request":{"noSEP":"0904R0080624V016929","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:45 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","4 - 008 - 018"],"nomorkartu":"0000036814364","tanggal":"2024-06-20"}
2024-06-20 12:54:46 {"nomorkartu":"0003556329669","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 021"]}
2024-06-20 12:54:46 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","5 - 008 - THT"],"nomorkartu":"0003070549517","tanggal":"2024-06-20"}
2024-06-20 12:54:46 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0002074512846","tanggal":"2024-06-20"}
2024-06-20 12:54:46 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","11 - 008 - PAR"],"nomorkartu":"0003034010373","tanggal":"2024-06-20"}
2024-06-20 12:54:47 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","3 - 008 - 018"],"nomorkartu":"0000810799479","tanggal":"2024-06-20"}
2024-06-20 12:54:47 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - URO"],"nomorkartu":"0001638723554","tanggal":"2024-06-20"}
2024-06-20 12:54:47 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","21 - 008 - 017"],"nomorkartu":"0001296790558","tanggal":"2024-06-20"}
2024-06-20 12:54:47 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0000036106029","tanggal":"2024-06-20"}
2024-06-20 12:54:48 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","16 - 008 - 008"],"nomorkartu":"0001103494746","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017636","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:48 {"nomorkartu":"0002962349954","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - THT"]}
2024-06-20 12:54:48 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - JAN"],"nomorkartu":"0001207392412","tanggal":"2024-06-20"}
2024-06-20 12:54:49 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0001726766594","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017403","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:49 {"nomorkartu":"0000152406742","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 021"]}
2024-06-20 12:54:49 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - PAR"],"nomorkartu":"0000036349165","tanggal":"2024-06-20"}
2024-06-20 12:54:49 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - PAR"],"nomorkartu":"0000037232447","tanggal":"2024-06-20"}
2024-06-20 12:54:50 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","0 - 008 - BTK"],"nomorkartu":"0002336148821","tanggal":"2024-06-20"}
2024-06-20 12:54:50 {"nomorkartu":"0000046393604","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - 021"]}
2024-06-20 12:54:50 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001731342328","tanggal":"2024-06-20"}
2024-06-20 12:54:50 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - THT"],"nomorkartu":"0001878015881","tanggal":"2024-06-20"}
2024-06-20 12:54:51 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","19 - 008 - 017"],"nomorkartu":"0001642176235","tanggal":"2024-06-20"}
2024-06-20 12:54:51 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0003144357551","tanggal":"2024-06-20"}
2024-06-20 12:54:51 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0001372888596","tanggal":"2024-06-20"}
2024-06-20 12:54:52 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0002266691477","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017570","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:52 {"nomorkartu":"0002357500724","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 017"]}
2024-06-20 12:54:52 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","21 - 008 - PAR"],"nomorkartu":"0000042381011","tanggal":"2024-06-20"}
2024-06-20 12:54:52 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001862145426","tanggal":"2024-06-20"}
2024-06-20 12:54:53 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0001289147308","tanggal":"2024-06-20"}
2024-06-20 12:54:53 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","13 - 008 - 008"],"nomorkartu":"0002076008837","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017173","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:54:53 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","11 - 008 - 017"],"nomorkartu":"0001784865587","tanggal":"2024-06-20"}
2024-06-20 12:54:54 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001796142857","tanggal":"2024-06-20"}
2024-06-20 12:54:54 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","14 - 008 - PAR"],"nomorkartu":"0001458004342","tanggal":"2024-06-20"}
2024-06-20 12:54:54 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0000047869615","tanggal":"2024-06-20"}
2024-06-20 12:54:55 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","5 - 008 - 018"],"nomorkartu":"0002356253267","tanggal":"2024-06-20"}
2024-06-20 12:54:55 {"nomorkartu":"0001220779945","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 008 - 021"]}
2024-06-20 12:54:55 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0000037484054","tanggal":"2024-06-20"}
2024-06-20 12:54:56 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","8 - 008 - 008"],"nomorkartu":"0001611535781","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V006938","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:54:56 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","11 - 008 - PAR"],"nomorkartu":"0001271608255","tanggal":"2024-06-20"}
2024-06-20 12:54:56 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0001461567813","tanggal":"2024-06-20"}
2024-06-20 12:54:57 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001454741886","tanggal":"2024-06-20"}
2024-06-20 12:54:57 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001470699573","tanggal":"2024-06-20"}
2024-06-20 12:54:57 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001325171024","tanggal":"2024-06-20"}
2024-06-20 12:54:57 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","2 - 008 - THT"],"nomorkartu":"0003558351712","tanggal":"2024-06-20"}
2024-06-20 12:54:58 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0003276530289","tanggal":"2024-06-20"}
2024-06-20 12:54:58 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001319571224","tanggal":"2024-06-20"}
2024-06-20 12:54:58 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0001304381237","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V000533","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:54:59 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0002695396634","tanggal":"2024-06-20"}
2024-06-20 12:54:59 {"nomorkartu":"0001632360644","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 021"]}
2024-06-20 12:54:59 {"nomorkartu":"0000818470192","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - OBG"]}
2024-06-20 12:55:00 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0003548346063","tanggal":"2024-06-20"}
2024-06-20 12:55:00 {"nomorkartu":"0001221317785","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 008"]}
2024-06-20 12:55:01 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0000042057696","tanggal":"2024-06-20"}
2024-06-20 12:55:01 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0000059685039","tanggal":"2024-06-20"}
2024-06-20 12:55:02 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0001142767091","tanggal":"2024-06-20"}
2024-06-20 12:55:02 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","14 - 008 - 017"],"nomorkartu":"0002328603816","tanggal":"2024-06-20"}
2024-06-20 12:55:03 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0002238377815","tanggal":"2024-06-20"}
2024-06-20 12:55:04 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","6 - 008 - PAR"],"nomorkartu":"0001766621338","tanggal":"2024-06-20"}
2024-06-20 12:55:05 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001435629519","tanggal":"2024-06-20"}
2024-06-20 12:55:05 {"nomorkartu":"0001882936991","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - THT"]}
2024-06-20 12:55:06 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0001732987539","tanggal":"2024-06-20"}
2024-06-20 12:55:07 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","1 - 008 - INT"],"nomorkartu":"0001213887791","tanggal":"2024-06-20"}
2024-06-20 12:55:08 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","15 - 008 - 021"],"nomorkartu":"0001792519661","tanggal":"2024-06-20"}
2024-06-20 12:55:09 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","10 - 008 - INT"],"nomorkartu":"0003568867244","tanggal":"2024-06-20"}
2024-06-20 12:55:09 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0003340058027","tanggal":"2024-06-20"}
2024-06-20 12:55:10 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001257753723","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016765","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:10 {"nomorkartu":"0001738888435","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 008 - OBG"]}
2024-06-20 12:55:11 {"nomorkartu":"0001156821647","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 008 - 021"]}
2024-06-20 12:55:11 {"nomorkartu":"0001325939499","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 008 - OBG"]}
2024-06-20 12:55:12 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0002103928525","tanggal":"2024-06-20"}
2024-06-20 12:55:12 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001601473151","tanggal":"2024-06-20"}
2024-06-20 12:55:13 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","11 - 008 - 008"],"nomorkartu":"0001825995712","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016822","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:13 {"nomorkartu":"0003083229213","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 021"]}
2024-06-20 12:55:13 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001845693729","tanggal":"2024-06-20"}
2024-06-20 12:55:14 {"nomorkartu":"0003005247407","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 017"]}
2024-06-20 12:55:14 {"nomorkartu":"0001458569136","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - 008 - 021"]}
2024-06-20 12:55:14 {"nomorkartu":"0002045736101","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 021"]}
2024-06-20 12:55:15 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","27 - 008 - 017"],"nomorkartu":"0000371582605","tanggal":"2024-06-20"}
2024-06-20 12:55:15 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001721553197","tanggal":"2024-06-20"}
2024-06-20 12:55:15 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0001315170494","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017499","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:16 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","14 - 008 - THT"],"nomorkartu":"0002231247802","tanggal":"2024-06-20"}
2024-06-20 12:55:16 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0001051756918","tanggal":"2024-06-20"}
2024-06-20 12:55:17 {"nomorkartu":"0000040572404","tanggal":"2024-06-20","catatan":["Rujukan RS","15 - 008 - 021"]}
2024-06-20 12:55:17 {"nomorkartu":"0000367314647","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 008 - OBG"]}
2024-06-20 12:55:17 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0003136107543","tanggal":"2024-06-20"}
2024-06-20 12:55:18 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0002066228109","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017916","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:18 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001336745351","tanggal":"2024-06-20"}
2024-06-20 12:55:19 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001257801939","tanggal":"2024-06-20"}
2024-06-20 12:55:19 {"nomorkartu":"0000048889991","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 008 - 008"],"request":{"noSEP":"0904R0080624V016148","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006407","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0000048889991","nama":"ENDANG SHOLIHAT","kelamin":"Laki-laki","tglLahir":"1961-05-18","namaDiagnosa":"C92.1 - Chronic myeloid leukaemia"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V016148', '216749', '008', '0904R0080624K006407', '0000048889991', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'ENDANG SOLIHAT', '1065')<br>MySQL server has gone away"}
2024-06-20 12:55:19 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan PCARE"," - 008 - 005"],"nomorkartu":"0001458205852","tanggal":"2024-06-20"}
2024-06-20 12:55:20 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001743473035","tanggal":"2024-06-20"}
2024-06-20 12:55:21 {"nomorkartu":"0000041252782","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 008"],"request":{"noSEP":"0904R0080624V017109","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:21 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","2 - 008 - 017"],"nomorkartu":"0001144658755","tanggal":"2024-06-20"}
2024-06-20 12:55:22 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0001215052582","tanggal":"2024-06-20"}
2024-06-20 12:55:22 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001315187381","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V015823","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006408","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0001315187381","nama":"TJOA KIE GOAN","kelamin":"Laki-laki","tglLahir":"1962-10-29","namaDiagnosa":"C85.9 - Non-Hodgkin's lymphoma, unspecified type"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V015823', '216749', '008', '0904R0080624K006408', '0001315187381', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'TJOA KIE GOAN , TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:55:23 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001446207164","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017240","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:23 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0000794810722","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017548","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:23 {"nomorkartu":"0001052612471","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 008 - 021"]}
2024-06-20 12:55:24 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","14 - 008 - 017"],"nomorkartu":"0001609955379","tanggal":"2024-06-20"}
2024-06-20 12:55:24 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","0 - 008 - 017"],"nomorkartu":"0001651368802","tanggal":"2024-06-20"}
2024-06-20 12:55:24 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","8 - 008 - THT"],"nomorkartu":"0001311651246","tanggal":"2024-06-20"}
2024-06-20 12:55:24 {"nomorkartu":"0002330468919","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 008"]}
2024-06-20 12:55:25 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 008"],"nomorkartu":"0001733152689","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017399","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:25 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","12 - 008 - 008"],"nomorkartu":"0002045955396","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V026125","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006409","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0002045955396","nama":"TRI PRIHATINI","kelamin":"Perempuan","tglLahir":"1990-12-27","namaDiagnosa":"Z48.8 - Other specified surgical follow-up care"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080524V026125', '216749', '008', '0904R0080624K006409', '0002045955396', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'TRI PRIHATINI, NY', '1065')<br>MySQL server has gone away"}
2024-06-20 12:55:26 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","4 - 008 - THT"],"nomorkartu":"0001434788008","tanggal":"2024-06-20"}
2024-06-20 12:55:26 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","4 - 008 - INT"],"nomorkartu":"0000043199976","tanggal":"2024-06-20"}
2024-06-20 12:55:26 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001468766294","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V009740","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006410","tglRencanaKontrol":"2024-06-20","namaDokter":"DR RESTI MULYA SARI","noKartu":"0001468766294","nama":"TAN SIE HIAN","kelamin":"Laki-laki","tglLahir":"1949-10-10","namaDiagnosa":"C34.9 - Malignant neoplasm of bronchus or lung, unspecified"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V009740', '216749', '008', '0904R0080624K006410', '0001468766294', 'DR RESTI MULYA SARI SpPD KHOM', '2024-06-20', 'TAN SIE HIAN, TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:55:27 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","12 - 008 - 021"],"nomorkartu":"0002240121025","tanggal":"2024-06-20"}
2024-06-20 12:55:27 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0002198868805","tanggal":"2024-06-20"}
2024-06-20 12:55:28 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0002140618048","tanggal":"2024-06-20"}
2024-06-20 12:55:29 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan PCARE"," - 008 - PAR"],"nomorkartu":"0001390388679","tanggal":"2024-06-20"}
2024-06-20 12:55:29 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001222443459","tanggal":"2024-06-20"}
2024-06-20 12:55:29 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","2 - 008 - INT"],"nomorkartu":"0000639459641","tanggal":"2024-06-20"}
2024-06-20 12:55:29 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - INT"],"nomorkartu":"0001173458169","tanggal":"2024-06-20"}
2024-06-20 12:55:30 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","7 - 008 - 018"],"nomorkartu":"0001884843595","tanggal":"2024-06-20"}
2024-06-20 12:55:30 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0002231281405","tanggal":"2024-06-20"}
2024-06-20 12:55:30 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0000816008073","tanggal":"2024-06-20"}
2024-06-20 12:55:31 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","7 - 008 - 008"],"nomorkartu":"0001140294069","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016935","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:31 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001807035344","tanggal":"2024-06-20"}
2024-06-20 12:55:31 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0002487612666","tanggal":"2024-06-20"}
2024-06-20 12:55:32 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","2 - 008 - 018"],"nomorkartu":"0001467656908","tanggal":"2024-06-20"}
2024-06-20 12:55:32 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001336937275","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018073","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:32 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","8 - 008 - 008"],"nomorkartu":"0001131212068","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017207","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:33 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","9 - 008 - THT"],"nomorkartu":"0001150276836","tanggal":"2024-06-20"}
2024-06-20 12:55:33 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","15 - 008 - 017"],"nomorkartu":"0001079079669","tanggal":"2024-06-20"}
2024-06-20 12:55:33 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","2 - 008 - 018"],"nomorkartu":"0002312784505","tanggal":"2024-06-20"}
2024-06-20 12:55:34 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","5 - 008 - 008"],"nomorkartu":"0000072435958","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018155","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:34 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","8 - 008 - THT"],"nomorkartu":"0002266003078","tanggal":"2024-06-20"}
2024-06-20 12:55:34 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","16 - 008 - 008"],"nomorkartu":"0003102740638","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017502","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:35 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","1 - 008 - INT"],"nomorkartu":"0001338888317","tanggal":"2024-06-20"}
2024-06-20 12:55:35 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - URO"],"nomorkartu":"0001288010935","tanggal":"2024-06-20"}
2024-06-20 12:55:35 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0002097892473","tanggal":"2024-06-20"}
2024-06-20 12:55:36 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","32 - 008 - 017"],"nomorkartu":"0003537411715","tanggal":"2024-06-20"}
2024-06-20 12:55:36 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","11 - 008 - 018"],"nomorkartu":"0001088046729","tanggal":"2024-06-20"}
2024-06-20 12:55:36 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","3 - 008 - INT"],"nomorkartu":"0001479283986","tanggal":"2024-06-20"}
2024-06-20 12:55:37 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","9 - 008 - 018"],"nomorkartu":"0000314767359","tanggal":"2024-06-20"}
2024-06-20 12:55:37 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001225529458","tanggal":"2024-06-20"}
2024-06-20 12:55:37 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0000343251112","tanggal":"2024-06-20"}
2024-06-20 12:55:37 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","22 - 008 - 017"],"nomorkartu":"0001465259409","tanggal":"2024-06-20"}
2024-06-20 12:55:38 {"nomorkartu":"0002435256303","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 008"]}
2024-06-20 12:55:38 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","6 - 008 - URO"],"nomorkartu":"0002355871296","tanggal":"2024-06-20"}
2024-06-20 12:55:38 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","11 - 008 - 017"],"nomorkartu":"0001453864241","tanggal":"2024-06-20"}
2024-06-20 12:55:38 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001221034689","tanggal":"2024-06-20"}
2024-06-20 12:55:39 {"nomorkartu":"0001210217253","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 021"]}
2024-06-20 12:55:39 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","8 - 008 - THT"],"nomorkartu":"0002488500876","tanggal":"2024-06-20"}
2024-06-20 12:55:39 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","5 - 008 - INT"],"nomorkartu":"0001600292452","tanggal":"2024-06-20"}
2024-06-20 12:55:40 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0003072015055","tanggal":"2024-06-20"}
2024-06-20 12:55:40 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","1 - 008 - 018"],"nomorkartu":"0002688590351","tanggal":"2024-06-20"}
2024-06-20 12:55:40 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","2 - 008 - 005"],"nomorkartu":"0001263553007","tanggal":"2024-06-20"}
2024-06-20 12:55:40 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - PAR"],"nomorkartu":"0001138053958","tanggal":"2024-06-20"}
2024-06-20 12:55:41 {"nomorkartu":"0002287575077","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 008 - 021"]}
2024-06-20 12:55:41 {"nomorkartu":"0001467530199","tanggal":"2024-06-20","catatan":["Rujukan RS","14 - 008 - OBG"]}
2024-06-20 12:55:41 {"nomorkartu":"0001720302546","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 008 - 008"],"request":{"noSEP":"0904R0080624V003849","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:55:42 {"nomorkartu":"0000043713371","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - KEM"]}
2024-06-20 12:55:43 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0002228175101","tanggal":"2024-06-20"}
2024-06-20 12:55:43 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001221220484","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018201","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:43 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","0 - 008 - 008"],"nomorkartu":"0001338254177","tanggal":"2024-06-20"}
2024-06-20 12:55:44 {"nomorkartu":"0002334041245","tanggal":"2024-06-20","catatan":["Rujukan RS","31 - 008 - 008"],"request":{"noSEP":"0904R0080624V017694","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:44 {"nomorkartu":"0001192636236","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 017"]}
2024-06-20 12:55:44 {"nomorkartu":"0001854351257","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 008 - 021"]}
2024-06-20 12:55:44 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","17 - 008 - PAR"],"nomorkartu":"0001591653958","tanggal":"2024-06-20"}
2024-06-20 12:55:45 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001463691295","tanggal":"2024-06-20"}
2024-06-20 12:55:45 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","0 - 008 - 017"],"nomorkartu":"0001827926368","tanggal":"2024-06-20"}
2024-06-20 12:55:45 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","8 - 008 - 017"],"nomorkartu":"0001378512426","tanggal":"2024-06-20"}
2024-06-20 12:55:46 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","6 - 008 - 008"],"nomorkartu":"0001328143252","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017714","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:46 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","17 - 008 - 017"],"nomorkartu":"0000800356623","tanggal":"2024-06-20"}
2024-06-20 12:55:46 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0000033509709","tanggal":"2024-06-20"}
2024-06-20 12:55:47 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0001882688275","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017599","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:47 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","5 - 008 - 008"],"nomorkartu":"0001774814411","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V013157","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Nomor SEP Sudah pernah digunakan!."},"response":null}}
2024-06-20 12:55:48 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001462336378","tanggal":"2024-06-20"}
2024-06-20 12:55:48 {"nomorkartu":"0000369857621","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 021"]}
2024-06-20 12:55:48 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0003071768286","tanggal":"2024-06-20"}
2024-06-20 12:55:49 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","11 - 008 - 017"],"nomorkartu":"0001296838956","tanggal":"2024-06-20"}
2024-06-20 12:55:49 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","4 - 008 - 018"],"nomorkartu":"0001648095546","tanggal":"2024-06-20"}
2024-06-20 12:55:49 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - URO"],"nomorkartu":"0002299512982","tanggal":"2024-06-20"}
2024-06-20 12:55:49 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0001656136181","tanggal":"2024-06-20"}
2024-06-20 12:55:50 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001630150086","tanggal":"2024-06-20"}
2024-06-20 12:55:50 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","11 - 008 - PAR"],"nomorkartu":"0002195778936","tanggal":"2024-06-20"}
2024-06-20 12:55:53 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","3 - 008 - URO"],"nomorkartu":"0002261557765","tanggal":"2024-06-20"}
2024-06-20 12:55:54 {"catatan":["Dokter DPJP di ubah dari 270328 menjadi 216749","Rujukan RS","7 - 008 - 017"],"nomorkartu":"0000043423086","tanggal":"2024-06-20"}
2024-06-20 12:55:54 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0000209590536","tanggal":"2024-06-20"}
2024-06-20 12:55:55 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001290268765","tanggal":"2024-06-20"}
2024-06-20 12:55:55 {"nomorkartu":"0001889460404","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 021"]}
2024-06-20 12:55:55 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","10 - 008 - 008"],"nomorkartu":"0001631264499","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017746","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:55 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","12 - 008 - 017"],"nomorkartu":"0001430677282","tanggal":"2024-06-20"}
2024-06-20 12:55:56 {"nomorkartu":"0000374023563","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - KEM"]}
2024-06-20 12:55:56 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","2 - 008 - THT"],"nomorkartu":"0003007450664","tanggal":"2024-06-20"}
2024-06-20 12:55:56 {"nomorkartu":"0001409500326","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 008 - 021"]}
2024-06-20 12:55:57 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0000002222133","tanggal":"2024-06-20"}
2024-06-20 12:55:57 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","7 - 008 - 018"],"nomorkartu":"0001439688587","tanggal":"2024-06-20"}
2024-06-20 12:55:57 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","4 - 008 - 008"],"nomorkartu":"0000049497175","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018124","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:55:58 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0001217985017","tanggal":"2024-06-20"}
2024-06-20 12:55:58 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","2 - 008 - 018"],"nomorkartu":"0001645201271","tanggal":"2024-06-20"}
2024-06-20 12:55:58 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan PCARE"," - 008 - 017"],"nomorkartu":"0001471891735","tanggal":"2024-06-20"}
2024-06-20 12:55:59 {"nomorkartu":"0000038220118","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 008"]}
2024-06-20 12:55:59 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","7 - 008 - 018"],"nomorkartu":"0001454588098","tanggal":"2024-06-20"}
2024-06-20 12:55:59 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","5 - 008 - 017"],"nomorkartu":"0001222928368","tanggal":"2024-06-20"}
2024-06-20 12:56:00 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","9 - 008 - BSY"],"nomorkartu":"0001219563821","tanggal":"2024-06-20"}
2024-06-20 12:56:00 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001652907341","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018147","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:00 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","2 - 008 - THT"],"nomorkartu":"0000050972938","tanggal":"2024-06-20"}
2024-06-20 12:56:01 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","15 - 008 - 017"],"nomorkartu":"0000030589222","tanggal":"2024-06-20"}
2024-06-20 12:56:01 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","14 - 008 - 018"],"nomorkartu":"0002334185212","tanggal":"2024-06-20"}
2024-06-20 12:56:01 {"catatan":["Dokter DPJP di ubah dari 222579 menjadi 216749","Rujukan RS","12 - 008 - PAR"],"nomorkartu":"0001462377058","tanggal":"2024-06-20"}
2024-06-20 12:56:02 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","3 - 008 - 008"],"nomorkartu":"0001624856973","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V025306","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Nomor SEP Sudah pernah digunakan!."},"response":null}}
2024-06-20 12:56:02 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","9 - 008 - 017"],"nomorkartu":"0001795540487","tanggal":"2024-06-20"}
2024-06-20 12:56:02 {"catatan":["Dokter DPJP di ubah dari 270350 menjadi 216749","Rujukan RS","2 - 008 - 008"],"nomorkartu":"0003069683638","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017538","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:03 {"catatan":["Dokter DPJP di ubah dari 267712 menjadi 216749","Rujukan RS","6 - 008 - 021"],"nomorkartu":"0001648513798","tanggal":"2024-06-20"}
2024-06-20 12:56:03 {"nomorkartu":"0000166679289","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - 021"]}
2024-06-20 12:56:03 {"nomorkartu":"0001864147397","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 021"]}
2024-06-20 12:56:04 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan PCARE"," - 008 - INT"],"nomorkartu":"0002203727117","tanggal":"2024-06-20"}
2024-06-20 12:56:04 {"nomorkartu":"0002226995043","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - 008"],"request":{"noSEP":"0904R0080624V017620","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:04 {"nomorkartu":"0002694640601","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 008 - 021"]}
2024-06-20 12:56:05 {"nomorkartu":"0000813644616","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - THT"]}
2024-06-20 12:56:05 {"catatan":["Dokter DPJP di ubah dari 216793 menjadi 216749","Rujukan RS","24 - 008 - 021"],"nomorkartu":"0002058900513","tanggal":"2024-06-20"}
2024-06-20 12:56:05 {"nomorkartu":"0000641371162","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 008 - 008"],"request":{"noSEP":"0904R0080624V017132","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:06 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0002612320828","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017571","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:06 {"nomorkartu":"0001148251239","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - 008 - OBG"]}
2024-06-20 12:56:06 {"nomorkartu":"0001260017245","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 008 - OBG"]}
2024-06-20 12:56:07 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","8 - 008 - 018"],"nomorkartu":"0000043076529","tanggal":"2024-06-20"}
2024-06-20 12:56:07 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 017"],"nomorkartu":"0001212042418","tanggal":"2024-06-20"}
2024-06-20 12:56:08 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - 008"],"nomorkartu":"0002081421483","tanggal":"2024-06-20"}
2024-06-20 12:56:08 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","15 - 008 - 017"],"nomorkartu":"0001729847406","tanggal":"2024-06-20"}
2024-06-20 12:56:09 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0000164563132","tanggal":"2024-06-20"}
2024-06-20 12:56:09 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001467327363","tanggal":"2024-06-20"}
2024-06-20 12:56:10 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan PCARE"," - 008 - KEM"],"nomorkartu":"0001220148112","tanggal":"2024-06-20"}
2024-06-20 12:56:10 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","10 - 008 - 017"],"nomorkartu":"0001387771277","tanggal":"2024-06-20"}
2024-06-20 12:56:10 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","3 - 008 - 017"],"nomorkartu":"0002303155449","tanggal":"2024-06-20"}
2024-06-20 12:56:11 {"catatan":["Dokter DPJP di ubah dari 216839 menjadi 216749","Rujukan RS","4 - 008 - 017"],"nomorkartu":"0002104969994","tanggal":"2024-06-20"}
2024-06-20 12:56:11 {"catatan":["Dokter DPJP di ubah dari 216867 menjadi 216749","Rujukan RS","1 - 008 - 008"],"nomorkartu":"0001462492269","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017890","kodeDokter":"216749","poliKontrol":"008","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:11 {"nomorkartu":"0003340085409","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 008 - OBG"]}
2024-06-20 12:56:12 {"nomorkartu":"0001517838456","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 008 - 021"]}
2024-06-20 12:56:12 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","7 - 008 - 018"],"nomorkartu":"0000046740881","tanggal":"2024-06-20"}
2024-06-20 12:56:12 {"catatan":["Dokter DPJP di ubah dari 216817 menjadi 216749","Rujukan RS","6 - 008 - 017"],"nomorkartu":"0001959340814","tanggal":"2024-06-20"}
2024-06-20 12:56:13 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0000814285631","tanggal":"2024-06-20"}
2024-06-20 12:56:14 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","0 - 168 - 017"],"nomorkartu":"0001635397468","tanggal":"2024-06-20"}
2024-06-20 12:56:14 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","5 - 168 - 021"],"nomorkartu":"0002222321207","tanggal":"2024-06-20"}
2024-06-20 12:56:14 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","6 - 168 - 017"],"nomorkartu":"0002054284659","tanggal":"2024-06-20"}
2024-06-20 12:56:14 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0003526892032","tanggal":"2024-06-20"}
2024-06-20 12:56:15 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 018"],"nomorkartu":"0000173754336","tanggal":"2024-06-20"}
2024-06-20 12:56:15 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","1 - 168 - 017"],"nomorkartu":"0000046760422","tanggal":"2024-06-20"}
2024-06-20 12:56:15 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","2 - 168 - URO"],"nomorkartu":"0001157291987","tanggal":"2024-06-20"}
2024-06-20 12:56:16 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan PCARE"," - 168 - 021"],"nomorkartu":"0000580716819","tanggal":"2024-06-20"}
2024-06-20 12:56:16 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","3 - 168 - 018"],"nomorkartu":"0003511974339","tanggal":"2024-06-20"}
2024-06-20 12:56:16 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","1 - 168 - 018"],"nomorkartu":"0000811503933","tanggal":"2024-06-20"}
2024-06-20 12:56:16 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","6 - 168 - 021"],"nomorkartu":"0002296521099","tanggal":"2024-06-20"}
2024-06-20 12:56:17 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","16 - 168 - 017"],"nomorkartu":"0001448242198","tanggal":"2024-06-20"}
2024-06-20 12:56:17 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","5 - 168 - 018"],"nomorkartu":"0001339316278","tanggal":"2024-06-20"}
2024-06-20 12:56:17 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","3 - 168 - INT"],"nomorkartu":"0001605985784","tanggal":"2024-06-20"}
2024-06-20 12:56:17 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","2 - 168 - PAR"],"nomorkartu":"0000803956858","tanggal":"2024-06-20"}
2024-06-20 12:56:18 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","25 - 168 - 017"],"nomorkartu":"0001766009395","tanggal":"2024-06-20"}
2024-06-20 12:56:18 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0001892855788","tanggal":"2024-06-20"}
2024-06-20 12:56:18 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","3 - 168 - THT"],"nomorkartu":"0002138588021","tanggal":"2024-06-20"}
2024-06-20 12:56:18 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","4 - 168 - 018"],"nomorkartu":"0000664909672","tanggal":"2024-06-20"}
2024-06-20 12:56:19 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","5 - 168 - PAR"],"nomorkartu":"0003057945175","tanggal":"2024-06-20"}
2024-06-20 12:56:19 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","5 - 168 - 017"],"nomorkartu":"0001224193138","tanggal":"2024-06-20"}
2024-06-20 12:56:19 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0002339560686","tanggal":"2024-06-20"}
2024-06-20 12:56:19 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","6 - 168 - 017"],"nomorkartu":"0002234668959","tanggal":"2024-06-20"}
2024-06-20 12:56:20 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0002359320399","tanggal":"2024-06-20"}
2024-06-20 12:56:20 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - KEM"],"nomorkartu":"0001643882782","tanggal":"2024-06-20"}
2024-06-20 12:56:20 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0000048078843","tanggal":"2024-06-20"}
2024-06-20 12:56:21 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","13 - 168 - BSY"],"nomorkartu":"0001129116251","tanggal":"2024-06-20"}
2024-06-20 12:56:21 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0002361575002","tanggal":"2024-06-20"}
2024-06-20 12:56:21 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","1 - 168 - 168"],"nomorkartu":"0001520514167","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V012297","kodeDokter":"40426","poliKontrol":"168","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:56:22 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","12 - 168 - PAR"],"nomorkartu":"0003144295337","tanggal":"2024-06-20"}
2024-06-20 12:56:22 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan PCARE"," - 168 - BSY"],"nomorkartu":"0002674614216","tanggal":"2024-06-20"}
2024-06-20 12:56:22 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - URO"],"nomorkartu":"0003534238214","tanggal":"2024-06-20"}
2024-06-20 12:56:23 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - THT"],"nomorkartu":"0001256985066","tanggal":"2024-06-20"}
2024-06-20 12:56:23 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0001803729892","tanggal":"2024-06-20"}
2024-06-20 12:56:23 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","23 - 168 - PAR"],"nomorkartu":"0001094648556","tanggal":"2024-06-20"}
2024-06-20 12:56:24 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0001637139352","tanggal":"2024-06-20"}
2024-06-20 12:56:24 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","24 - 168 - THT"],"nomorkartu":"0001128496522","tanggal":"2024-06-20"}
2024-06-20 12:56:24 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","9 - 168 - SAR"],"nomorkartu":"0001621544771","tanggal":"2024-06-20"}
2024-06-20 12:56:25 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","36 - 168 - URO"],"nomorkartu":"0000047730791","tanggal":"2024-06-20"}
2024-06-20 12:56:25 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","13 - 168 - PAR"],"nomorkartu":"0002259085781","tanggal":"2024-06-20"}
2024-06-20 12:56:25 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - BTK"],"nomorkartu":"0002514026597","tanggal":"2024-06-20"}
2024-06-20 12:56:26 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0001379600853","tanggal":"2024-06-20"}
2024-06-20 12:56:26 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","12 - 168 - THT"],"nomorkartu":"0003102495941","tanggal":"2024-06-20"}
2024-06-20 12:56:26 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","14 - 168 - 017"],"nomorkartu":"0000001087108","tanggal":"2024-06-20"}
2024-06-20 12:56:26 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","27 - 168 - THT"],"nomorkartu":"0000000788523","tanggal":"2024-06-20"}
2024-06-20 12:56:27 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","10 - 168 - 017"],"nomorkartu":"0001222796046","tanggal":"2024-06-20"}
2024-06-20 12:56:27 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","28 - 168 - OBG"],"nomorkartu":"0000893513147","tanggal":"2024-06-20"}
2024-06-20 12:56:27 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","21 - 168 - OBG"],"nomorkartu":"0002338590317","tanggal":"2024-06-20"}
2024-06-20 12:56:27 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","38 - 168 - BSY"],"nomorkartu":"0003162172702","tanggal":"2024-06-20"}
2024-06-20 12:56:28 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - PAR"],"nomorkartu":"0001655556423","tanggal":"2024-06-20"}
2024-06-20 12:56:28 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","16 - 168 - 017"],"nomorkartu":"0001590243726","tanggal":"2024-06-20"}
2024-06-20 12:56:28 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","15 - 168 - SAR"],"nomorkartu":"0001741077639","tanggal":"2024-06-20"}
2024-06-20 12:56:29 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","25 - 168 - BSY"],"nomorkartu":"0003176404367","tanggal":"2024-06-20"}
2024-06-20 12:56:29 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","14 - 168 - OBG"],"nomorkartu":"0002904083763","tanggal":"2024-06-20"}
2024-06-20 12:56:29 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","28 - 168 - URO"],"nomorkartu":"0002098118272","tanggal":"2024-06-20"}
2024-06-20 12:56:29 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","9 - 168 - PAR"],"nomorkartu":"0001135255105","tanggal":"2024-06-20"}
2024-06-20 12:56:30 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","32 - 168 - THT"],"nomorkartu":"0001460417422","tanggal":"2024-06-20"}
2024-06-20 12:56:30 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","12 - 168 - URO"],"nomorkartu":"0001425635188","tanggal":"2024-06-20"}
2024-06-20 12:56:30 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","19 - 168 - 008"],"nomorkartu":"0001634327346","tanggal":"2024-06-20"}
2024-06-20 12:56:30 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","18 - 168 - THT"],"nomorkartu":"0001460346985","tanggal":"2024-06-20"}
2024-06-20 12:56:31 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","18 - 168 - 021"],"nomorkartu":"0003326122304","tanggal":"2024-06-20"}
2024-06-20 12:56:31 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","12 - 168 - 018"],"nomorkartu":"0000024609251","tanggal":"2024-06-20"}
2024-06-20 12:56:31 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","30 - 168 - 017"],"nomorkartu":"0001322928832","tanggal":"2024-06-20"}
2024-06-20 12:56:32 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - OBG"],"nomorkartu":"0001465814024","tanggal":"2024-06-20"}
2024-06-20 12:56:32 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","10 - 168 - 005"],"nomorkartu":"0001208660477","tanggal":"2024-06-20"}
2024-06-20 12:56:32 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001805547407","tanggal":"2024-06-20"}
2024-06-20 12:56:32 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","2 - 168 - 030"],"nomorkartu":"0001643824787","tanggal":"2024-06-20"}
2024-06-20 12:56:33 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","20 - 168 - URO"],"nomorkartu":"0000041313789","tanggal":"2024-06-20"}
2024-06-20 12:56:33 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan PCARE"," - 168 - KEM"],"nomorkartu":"0001147036498","tanggal":"2024-06-20"}
2024-06-20 12:56:33 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0002261739183","tanggal":"2024-06-20"}
2024-06-20 12:56:34 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","12 - 168 - 017"],"nomorkartu":"0001716879554","tanggal":"2024-06-20"}
2024-06-20 12:56:34 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","14 - 168 - 017"],"nomorkartu":"0003128696054","tanggal":"2024-06-20"}
2024-06-20 12:56:34 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","19 - 168 - THT"],"nomorkartu":"0001637301576","tanggal":"2024-06-20"}
2024-06-20 12:56:34 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","2 - 168 - THT"],"nomorkartu":"0001774097796","tanggal":"2024-06-20"}
2024-06-20 12:56:35 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - INT"],"nomorkartu":"0002332819359","tanggal":"2024-06-20"}
2024-06-20 12:56:35 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","17 - 168 - 021"],"nomorkartu":"0001785076356","tanggal":"2024-06-20"}
2024-06-20 12:56:35 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","28 - 168 - 017"],"nomorkartu":"0001319555812","tanggal":"2024-06-20"}
2024-06-20 12:56:35 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001256658028","tanggal":"2024-06-20"}
2024-06-20 12:56:36 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","16 - 168 - THT"],"nomorkartu":"0001847843087","tanggal":"2024-06-20"}
2024-06-20 12:56:36 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","31 - 168 - THT"],"nomorkartu":"0003342455739","tanggal":"2024-06-20"}
2024-06-20 12:56:36 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","23 - 168 - 017"],"nomorkartu":"0000512828381","tanggal":"2024-06-20"}
2024-06-20 12:56:36 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","5 - 168 - 017"],"nomorkartu":"0001338823451","tanggal":"2024-06-20"}
2024-06-20 12:56:37 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","12 - 168 - 021"],"nomorkartu":"0002519723092","tanggal":"2024-06-20"}
2024-06-20 12:56:37 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","3 - 168 - BSY"],"nomorkartu":"0001307655516","tanggal":"2024-06-20"}
2024-06-20 12:56:38 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001102013987","tanggal":"2024-06-20"}
2024-06-20 12:56:38 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0003290919041","tanggal":"2024-06-20"}
2024-06-20 12:56:38 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","32 - 168 - 017"],"nomorkartu":"0000386146236","tanggal":"2024-06-20"}
2024-06-20 12:56:39 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan PCARE"," - 168 - RAT"],"nomorkartu":"0003533840199","tanggal":"2024-06-20"}
2024-06-20 12:56:39 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","26 - 168 - 017"],"nomorkartu":"0001291220021","tanggal":"2024-06-20"}
2024-06-20 12:56:39 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - THT"],"nomorkartu":"0001389726606","tanggal":"2024-06-20"}
2024-06-20 12:56:40 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","33 - 168 - 168"],"nomorkartu":"0001645871264","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017045","kodeDokter":"40426","poliKontrol":"168","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:40 {"nomorkartu":"0001221564521","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 168 - 021"]}
2024-06-20 12:56:40 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","1 - 168 - 017"],"nomorkartu":"0003075388841","tanggal":"2024-06-20"}
2024-06-20 12:56:41 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","19 - 168 - 017"],"nomorkartu":"0001136640734","tanggal":"2024-06-20"}
2024-06-20 12:56:41 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan PCARE"," - 168 - 008"],"nomorkartu":"0001653297467","tanggal":"2024-06-20"}
2024-06-20 12:56:41 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","11 - 168 - 021"],"nomorkartu":"0002874887728","tanggal":"2024-06-20"}
2024-06-20 12:56:41 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","26 - 168 - PAR"],"nomorkartu":"0002240185228","tanggal":"2024-06-20"}
2024-06-20 12:56:42 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0002429649279","tanggal":"2024-06-20"}
2024-06-20 12:56:42 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0001156767489","tanggal":"2024-06-20"}
2024-06-20 12:56:42 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","22 - 168 - 017"],"nomorkartu":"0002318107094","tanggal":"2024-06-20"}
2024-06-20 12:56:43 {"nomorkartu":"0001207292049","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 168 - 017"]}
2024-06-20 12:56:43 {"nomorkartu":"0001302673724","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 168 - 017"]}
2024-06-20 12:56:43 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","6 - 168 - 021"],"nomorkartu":"0001745201992","tanggal":"2024-06-20"}
2024-06-20 12:56:43 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","12 - 168 - 018"],"nomorkartu":"0003571195375","tanggal":"2024-06-20"}
2024-06-20 12:56:44 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","3 - 168 - 008"],"nomorkartu":"0001458914286","tanggal":"2024-06-20"}
2024-06-20 12:56:44 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0000542962708","tanggal":"2024-06-20"}
2024-06-20 12:56:44 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001501214376","tanggal":"2024-06-20"}
2024-06-20 12:56:45 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","12 - 168 - 017"],"nomorkartu":"0002378097832","tanggal":"2024-06-20"}
2024-06-20 12:56:45 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","5 - 168 - 008"],"nomorkartu":"0000036755289","tanggal":"2024-06-20"}
2024-06-20 12:56:45 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0001148478671","tanggal":"2024-06-20"}
2024-06-20 12:56:45 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0001881515518","tanggal":"2024-06-20"}
2024-06-20 12:56:46 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","12 - 168 - 017"],"nomorkartu":"0001225447266","tanggal":"2024-06-20"}
2024-06-20 12:56:46 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0001384113251","tanggal":"2024-06-20"}
2024-06-20 12:56:46 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0002687147177","tanggal":"2024-06-20"}
2024-06-20 12:56:46 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","9 - 168 - OBG"],"nomorkartu":"0000376253943","tanggal":"2024-06-20"}
2024-06-20 12:56:47 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0002064137624","tanggal":"2024-06-20"}
2024-06-20 12:56:47 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","25 - 168 - PAR"],"nomorkartu":"0001875015358","tanggal":"2024-06-20"}
2024-06-20 12:56:47 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","26 - 168 - 021"],"nomorkartu":"0002063637281","tanggal":"2024-06-20"}
2024-06-20 12:56:47 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","35 - 168 - URO"],"nomorkartu":"0001741764341","tanggal":"2024-06-20"}
2024-06-20 12:56:48 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","38 - 168 - 017"],"nomorkartu":"0001196988006","tanggal":"2024-06-20"}
2024-06-20 12:56:48 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","31 - 168 - 017"],"nomorkartu":"0001826150556","tanggal":"2024-06-20"}
2024-06-20 12:56:48 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0002340186041","tanggal":"2024-06-20"}
2024-06-20 12:56:49 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - RAT"],"nomorkartu":"0001400234938","tanggal":"2024-06-20"}
2024-06-20 12:56:49 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0000366295678","tanggal":"2024-06-20"}
2024-06-20 12:56:49 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","21 - 168 - PAR"],"nomorkartu":"0001465777495","tanggal":"2024-06-20"}
2024-06-20 12:56:49 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","19 - 168 - 017"],"nomorkartu":"0001310519081","tanggal":"2024-06-20"}
2024-06-20 12:56:49 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","6 - 168 - 017"],"nomorkartu":"0003562351874","tanggal":"2024-06-20"}
2024-06-20 12:56:50 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","23 - 168 - 018"],"nomorkartu":"0000048428008","tanggal":"2024-06-20"}
2024-06-20 12:56:50 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","23 - 168 - 021"],"nomorkartu":"0002298706356","tanggal":"2024-06-20"}
2024-06-20 12:56:50 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","17 - 168 - 017"],"nomorkartu":"0001259970186","tanggal":"2024-06-20"}
2024-06-20 12:56:51 {"nomorkartu":"0002057971871","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - 168 - 017"]}
2024-06-20 12:56:51 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0001631785274","tanggal":"2024-06-20"}
2024-06-20 12:56:51 {"nomorkartu":"0002755936078","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 168 - 017"]}
2024-06-20 12:56:51 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - BSY"],"nomorkartu":"0002229724282","tanggal":"2024-06-20"}
2024-06-20 12:56:52 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","20 - 168 - 018"],"nomorkartu":"0002076463991","tanggal":"2024-06-20"}
2024-06-20 12:56:52 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","26 - 168 - OBG"],"nomorkartu":"0001220674645","tanggal":"2024-06-20"}
2024-06-20 12:56:52 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","17 - 168 - 017"],"nomorkartu":"0000377130688","tanggal":"2024-06-20"}
2024-06-20 12:56:52 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0002261707694","tanggal":"2024-06-20"}
2024-06-20 12:56:53 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","15 - 168 - SAR"],"nomorkartu":"0002089115032","tanggal":"2024-06-20"}
2024-06-20 12:56:53 {"nomorkartu":"0002899208744","tanggal":"2024-06-20","catatan":["Rujukan RS","27 - 168 - 017"]}
2024-06-20 12:56:53 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan PCARE"," - 168 - 018"],"nomorkartu":"0001452909069","tanggal":"2024-06-20"}
2024-06-20 12:56:53 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","18 - 168 - THT"],"nomorkartu":"0003268811722","tanggal":"2024-06-20"}
2024-06-20 12:56:54 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0003009954205","tanggal":"2024-06-20"}
2024-06-20 12:56:54 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","22 - 168 - 018"],"nomorkartu":"0000792807581","tanggal":"2024-06-20"}
2024-06-20 12:56:54 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","22 - 168 - 017"],"nomorkartu":"0003261848152","tanggal":"2024-06-20"}
2024-06-20 12:56:55 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","27 - 168 - 168"],"nomorkartu":"0003131555398","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017221","kodeDokter":"40426","poliKontrol":"168","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:56:55 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - URO"],"nomorkartu":"0002610160571","tanggal":"2024-06-20"}
2024-06-20 12:56:55 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","22 - 168 - 017"],"nomorkartu":"0002758858964","tanggal":"2024-06-20"}
2024-06-20 12:56:56 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan PCARE"," - 168 - 018"],"nomorkartu":"0000028203153","tanggal":"2024-06-20"}
2024-06-20 12:56:56 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","18 - 168 - 017"],"nomorkartu":"0001336309953","tanggal":"2024-06-20"}
2024-06-20 12:56:56 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","14 - 168 - 017"],"nomorkartu":"0002336480954","tanggal":"2024-06-20"}
2024-06-20 12:56:57 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","11 - 168 - 018"],"nomorkartu":"0001129652403","tanggal":"2024-06-20"}
2024-06-20 12:56:57 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001616085674","tanggal":"2024-06-20"}
2024-06-20 12:56:57 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","8 - 168 - OBG"],"nomorkartu":"0003342855576","tanggal":"2024-06-20"}
2024-06-20 12:56:57 {"nomorkartu":"0001409506007","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 168 - 017"]}
2024-06-20 12:56:58 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","17 - 168 - 021"],"nomorkartu":"0001967475677","tanggal":"2024-06-20"}
2024-06-20 12:56:58 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 021"],"nomorkartu":"0001467212433","tanggal":"2024-06-20"}
2024-06-20 12:56:58 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","8 - 168 - 017"],"nomorkartu":"0000049669266","tanggal":"2024-06-20"}
2024-06-20 12:56:58 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","30 - 168 - 021"],"nomorkartu":"0000509549207","tanggal":"2024-06-20"}
2024-06-20 12:56:59 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","19 - 168 - 017"],"nomorkartu":"0001628490328","tanggal":"2024-06-20"}
2024-06-20 12:56:59 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","35 - 168 - 017"],"nomorkartu":"0002563976889","tanggal":"2024-06-20"}
2024-06-20 12:56:59 {"nomorkartu":"0001469598996","tanggal":"2024-06-20","catatan":["Rujukan RS","17 - 168 - KDN"]}
2024-06-20 12:57:00 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","19 - 168 - 017"],"nomorkartu":"0000114798497","tanggal":"2024-06-20"}
2024-06-20 12:57:00 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0001470358293","tanggal":"2024-06-20"}
2024-06-20 12:57:00 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","7 - 168 - 018"],"nomorkartu":"0002691104883","tanggal":"2024-06-20"}
2024-06-20 12:57:00 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","7 - 168 - 017"],"nomorkartu":"0001370394202","tanggal":"2024-06-20"}
2024-06-20 12:57:01 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0003524966166","tanggal":"2024-06-20"}
2024-06-20 12:57:01 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0000792125144","tanggal":"2024-06-20"}
2024-06-20 12:57:01 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","13 - 168 - BSY"],"nomorkartu":"0000815235322","tanggal":"2024-06-20"}
2024-06-20 12:57:01 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","12 - 168 - 017"],"nomorkartu":"0001660409357","tanggal":"2024-06-20"}
2024-06-20 12:57:02 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0000375544258","tanggal":"2024-06-20"}
2024-06-20 12:57:02 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","12 - 168 - 008"],"nomorkartu":"0001838036046","tanggal":"2024-06-20"}
2024-06-20 12:57:02 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","5 - 168 - 018"],"nomorkartu":"0003003488515","tanggal":"2024-06-20"}
2024-06-20 12:57:02 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","25 - 168 - 017"],"nomorkartu":"0001053636849","tanggal":"2024-06-20"}
2024-06-20 12:57:03 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","30 - 168 - 017"],"nomorkartu":"0002925083553","tanggal":"2024-06-20"}
2024-06-20 12:57:03 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","20 - 168 - 017"],"nomorkartu":"0000046298744","tanggal":"2024-06-20"}
2024-06-20 12:57:03 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0000817478943","tanggal":"2024-06-20"}
2024-06-20 12:57:04 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0001726691534","tanggal":"2024-06-20"}
2024-06-20 12:57:04 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","16 - 168 - 017"],"nomorkartu":"0001609017726","tanggal":"2024-06-20"}
2024-06-20 12:57:04 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0001584899807","tanggal":"2024-06-20"}
2024-06-20 12:57:06 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","25 - 168 - 017"],"nomorkartu":"0002098351495","tanggal":"2024-06-20"}
2024-06-20 12:57:06 {"nomorkartu":"0001462137041","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - 168 - 168"],"request":{"noSEP":"0904R0080624V017915","kodeDokter":"40426","poliKontrol":"168","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:07 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","17 - 168 - 017"],"nomorkartu":"0001780389516","tanggal":"2024-06-20"}
2024-06-20 12:57:07 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","15 - 168 - 018"],"nomorkartu":"0002209882331","tanggal":"2024-06-20"}
2024-06-20 12:57:07 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","27 - 168 - 018"],"nomorkartu":"0001128728755","tanggal":"2024-06-20"}
2024-06-20 12:57:08 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","23 - 168 - BSY"],"nomorkartu":"0002329438342","tanggal":"2024-06-20"}
2024-06-20 12:57:08 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0001465357094","tanggal":"2024-06-20"}
2024-06-20 12:57:08 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","13 - 168 - 017"],"nomorkartu":"0002361621622","tanggal":"2024-06-20"}
2024-06-20 12:57:08 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","13 - 168 - THT"],"nomorkartu":"0000159708767","tanggal":"2024-06-20"}
2024-06-20 12:57:08 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","1 - 168 - ANA"],"nomorkartu":"0002082019307","tanggal":"2024-06-20"}
2024-06-20 12:57:09 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","10 - 168 - 018"],"nomorkartu":"0001735249105","tanggal":"2024-06-20"}
2024-06-20 12:57:09 {"nomorkartu":"0000803497915","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 168 - 017"]}
2024-06-20 12:57:09 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001145844347","tanggal":"2024-06-20"}
2024-06-20 12:57:10 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","7 - 168 - 021"],"nomorkartu":"0003530280745","tanggal":"2024-06-20"}
2024-06-20 12:57:10 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","10 - 168 - 017"],"nomorkartu":"0000042705404","tanggal":"2024-06-20"}
2024-06-20 12:57:10 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","3 - 168 - OBG"],"nomorkartu":"0001457156294","tanggal":"2024-06-20"}
2024-06-20 12:57:10 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","1 - 168 - 021"],"nomorkartu":"0001286733644","tanggal":"2024-06-20"}
2024-06-20 12:57:11 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0001720939004","tanggal":"2024-06-20"}
2024-06-20 12:57:11 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","4 - 168 - SAR"],"nomorkartu":"0002459734762","tanggal":"2024-06-20"}
2024-06-20 12:57:11 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","10 - 168 - 017"],"nomorkartu":"0002911889722","tanggal":"2024-06-20"}
2024-06-20 12:57:11 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","23 - 168 - BSY"],"nomorkartu":"0002255461334","tanggal":"2024-06-20"}
2024-06-20 12:57:12 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","23 - 168 - PAR"],"nomorkartu":"0001806902436","tanggal":"2024-06-20"}
2024-06-20 12:57:12 {"nomorkartu":"0001054028529","tanggal":"2024-06-20","catatan":["Rujukan RS","15 - 168 - PAR"]}
2024-06-20 12:57:12 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","19 - 168 - 018"],"nomorkartu":"0002487801003","tanggal":"2024-06-20"}
2024-06-20 12:57:12 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0001868984111","tanggal":"2024-06-20"}
2024-06-20 12:57:13 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","19 - 168 - THT"],"nomorkartu":"0001970120092","tanggal":"2024-06-20"}
2024-06-20 12:57:13 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0003109796897","tanggal":"2024-06-20"}
2024-06-20 12:57:13 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","23 - 168 - 017"],"nomorkartu":"0002328911111","tanggal":"2024-06-20"}
2024-06-20 12:57:13 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","26 - 168 - SAR"],"nomorkartu":"0000209889202","tanggal":"2024-06-20"}
2024-06-20 12:57:14 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","5 - 168 - THT"],"nomorkartu":"0001457601671","tanggal":"2024-06-20"}
2024-06-20 12:57:14 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","5 - 168 - 017"],"nomorkartu":"0001189165037","tanggal":"2024-06-20"}
2024-06-20 12:57:14 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","10 - 168 - THT"],"nomorkartu":"0000510676648","tanggal":"2024-06-20"}
2024-06-20 12:57:14 {"nomorkartu":"0001796754993","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 168 - THT"]}
2024-06-20 12:57:15 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","4 - 168 - 017"],"nomorkartu":"0003526122159","tanggal":"2024-06-20"}
2024-06-20 12:57:15 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0001383855748","tanggal":"2024-06-20"}
2024-06-20 12:57:15 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","1 - 168 - 021"],"nomorkartu":"0003272410798","tanggal":"2024-06-20"}
2024-06-20 12:57:15 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","10 - 168 - 017"],"nomorkartu":"0001131589113","tanggal":"2024-06-20"}
2024-06-20 12:57:16 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","16 - 168 - BSY"],"nomorkartu":"0002604829511","tanggal":"2024-06-20"}
2024-06-20 12:57:17 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","7 - 168 - BSY"],"nomorkartu":"0001324881066","tanggal":"2024-06-20"}
2024-06-20 12:57:17 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","7 - 168 - PAR"],"nomorkartu":"0001796752067","tanggal":"2024-06-20"}
2024-06-20 12:57:18 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","7 - 168 - ORT"],"nomorkartu":"0001590507674","tanggal":"2024-06-20"}
2024-06-20 12:57:18 {"nomorkartu":"0001221537699","tanggal":"2024-06-20","catatan":["Rujukan RS","16 - 168 - THT"]}
2024-06-20 12:57:18 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","1 - 168 - 017"],"nomorkartu":"0003521271036","tanggal":"2024-06-20"}
2024-06-20 12:57:18 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","10 - 168 - 021"],"nomorkartu":"0001382673767","tanggal":"2024-06-20"}
2024-06-20 12:57:19 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","19 - 168 - JAN"],"nomorkartu":"0000873350605","tanggal":"2024-06-20"}
2024-06-20 12:57:19 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","1 - 168 - 017"],"nomorkartu":"0001786424319","tanggal":"2024-06-20"}
2024-06-20 12:57:19 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","7 - 168 - 018"],"nomorkartu":"0002135469543","tanggal":"2024-06-20"}
2024-06-20 12:57:20 {"nomorkartu":"0001422002351","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 168 - 017"]}
2024-06-20 12:57:20 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","4 - 168 - SAR"],"nomorkartu":"0001381822075","tanggal":"2024-06-20"}
2024-06-20 12:57:20 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","8 - 168 - 017"],"nomorkartu":"0001430861927","tanggal":"2024-06-20"}
2024-06-20 12:57:20 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","15 - 168 - URO"],"nomorkartu":"0002357608059","tanggal":"2024-06-20"}
2024-06-20 12:57:21 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan PCARE"," - 168 - SAR"],"nomorkartu":"0002474377558","tanggal":"2024-06-20"}
2024-06-20 12:57:21 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","10 - 168 - 021"],"nomorkartu":"0001857230559","tanggal":"2024-06-20"}
2024-06-20 12:57:23 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","5 - 168 - 017"],"nomorkartu":"0001596769424","tanggal":"2024-06-20"}
2024-06-20 12:57:23 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","15 - 168 - BTK"],"nomorkartu":"0001400227705","tanggal":"2024-06-20"}
2024-06-20 12:57:23 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","2 - 168 - 021"],"nomorkartu":"0003084396524","tanggal":"2024-06-20"}
2024-06-20 12:57:23 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","13 - 168 - 017"],"nomorkartu":"0001266318753","tanggal":"2024-06-20"}
2024-06-20 12:57:24 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","12 - 168 - OBG"],"nomorkartu":"0003341984106","tanggal":"2024-06-20"}
2024-06-20 12:57:24 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","7 - 168 - 021"],"nomorkartu":"0000068283663","tanggal":"2024-06-20"}
2024-06-20 12:57:24 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","14 - 168 - 021"],"nomorkartu":"0000036850983","tanggal":"2024-06-20"}
2024-06-20 12:57:25 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0002490165606","tanggal":"2024-06-20"}
2024-06-20 12:57:25 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","3 - 168 - 017"],"nomorkartu":"0000036904509","tanggal":"2024-06-20"}
2024-06-20 12:57:25 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","15 - 168 - 017"],"nomorkartu":"0001189344688","tanggal":"2024-06-20"}
2024-06-20 12:57:25 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","27 - 168 - 017"],"nomorkartu":"0001219577433","tanggal":"2024-06-20"}
2024-06-20 12:57:26 {"catatan":["Dokter DPJP di ubah dari 217458 menjadi 40426","Rujukan RS","7 - 168 - 017"],"nomorkartu":"0001278692392","tanggal":"2024-06-20"}
2024-06-20 12:57:26 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","33 - 168 - 021"],"nomorkartu":"0002306036384","tanggal":"2024-06-20"}
2024-06-20 12:57:26 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","8 - 168 - 017"],"nomorkartu":"0001384395309","tanggal":"2024-06-20"}
2024-06-20 12:57:26 {"catatan":["Dokter DPJP di ubah dari 282078 menjadi 40426","Rujukan RS","18 - 168 - SAR"],"nomorkartu":"0001739709022","tanggal":"2024-06-20"}
2024-06-20 12:57:27 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0001338581529","tanggal":"2024-06-20"}
2024-06-20 12:57:27 {"nomorkartu":"0001247713727","tanggal":"2024-06-20","catatan":["Rujukan RS","11 - 168 - 017"]}
2024-06-20 12:57:27 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","30 - 168 - 017"],"nomorkartu":"0000037842197","tanggal":"2024-06-20"}
2024-06-20 12:57:28 {"nomorkartu":"0000038817246","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 168 - KEM"]}
2024-06-20 12:57:28 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0001100374659","tanggal":"2024-06-20"}
2024-06-20 12:57:29 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","17 - 168 - BSY"],"nomorkartu":"0001844384837","tanggal":"2024-06-20"}
2024-06-20 12:57:29 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","11 - 168 - 017"],"nomorkartu":"0001319088633","tanggal":"2024-06-20"}
2024-06-20 12:57:29 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","30 - 168 - 017"],"nomorkartu":"0000121725988","tanggal":"2024-06-20"}
2024-06-20 12:57:29 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","9 - 168 - 017"],"nomorkartu":"0000038112175","tanggal":"2024-06-20"}
2024-06-20 12:57:30 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","2 - 168 - 017"],"nomorkartu":"0001474333334","tanggal":"2024-06-20"}
2024-06-20 12:57:30 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan PCARE"," - 168 - RAT"],"nomorkartu":"0002334821444","tanggal":"2024-06-20"}
2024-06-20 12:57:31 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","12 - 168 - 021"],"nomorkartu":"0000818560844","tanggal":"2024-06-20"}
2024-06-20 12:57:31 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan PCARE"," - 168 - KEM"],"nomorkartu":"0000039374032","tanggal":"2024-06-20"}
2024-06-20 12:57:31 {"catatan":["Dokter DPJP di ubah dari 40428 menjadi 40426","Rujukan RS","15 - 168 - 018"],"nomorkartu":"0002333336455","tanggal":"2024-06-20"}
2024-06-20 12:57:32 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","16 - 168 - 017"],"nomorkartu":"0000148196891","tanggal":"2024-06-20"}
2024-06-20 12:57:32 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","26 - 168 - 017"],"nomorkartu":"0000816534393","tanggal":"2024-06-20"}
2024-06-20 12:57:32 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","1 - 168 - 017"],"nomorkartu":"0002323483672","tanggal":"2024-06-20"}
2024-06-20 12:57:32 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan PCARE"," - 168 - 021"],"nomorkartu":"0002077007398","tanggal":"2024-06-20"}
2024-06-20 12:57:33 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","14 - 168 - THT"],"nomorkartu":"0002915128146","tanggal":"2024-06-20"}
2024-06-20 12:57:33 {"nomorkartu":"0000815181344","tanggal":"2024-06-20","catatan":["Rujukan RS","12 - 168 - 017"]}
2024-06-20 12:57:33 {"catatan":["Dokter DPJP di ubah dari 273396 menjadi 40426","Rujukan RS","14 - 168 - ORT"],"nomorkartu":"0000149362312","tanggal":"2024-06-20"}
2024-06-20 12:57:33 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","17 - 168 - THT"],"nomorkartu":"0001129047298","tanggal":"2024-06-20"}
2024-06-20 12:57:34 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","18 - 168 - BSY"],"nomorkartu":"0002563727286","tanggal":"2024-06-20"}
2024-06-20 12:57:34 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","31 - 168 - 017"],"nomorkartu":"0001374850811","tanggal":"2024-06-20"}
2024-06-20 12:57:34 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","12 - 168 - 017"],"nomorkartu":"0002039394418","tanggal":"2024-06-20"}
2024-06-20 12:57:34 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","7 - 168 - 008"],"nomorkartu":"0000032993122","tanggal":"2024-06-20"}
2024-06-20 12:57:35 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan PCARE"," - 168 - 017"],"nomorkartu":"0000039914976","tanggal":"2024-06-20"}
2024-06-20 12:57:35 {"catatan":["Dokter DPJP di ubah dari 217595 menjadi 40426","Rujukan RS","10 - 168 - BSY"],"nomorkartu":"0001729726301","tanggal":"2024-06-20"}
2024-06-20 12:57:35 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","3 - 168 - OBG"],"nomorkartu":"0001301841134","tanggal":"2024-06-20"}
2024-06-20 12:57:36 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","2 - 168 - 021"],"nomorkartu":"0002936507062","tanggal":"2024-06-20"}
2024-06-20 12:57:36 {"catatan":["Dokter DPJP di ubah dari 217565 menjadi 40426","Rujukan RS","19 - 168 - BSY"],"nomorkartu":"0002491765626","tanggal":"2024-06-20"}
2024-06-20 12:57:36 {"catatan":["Dokter DPJP di ubah dari 217539 menjadi 40426","Rujukan RS","2 - 168 - 021"],"nomorkartu":"0003341840736","tanggal":"2024-06-20"}
2024-06-20 12:57:36 {"nomorkartu":"0001821799168","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - KDN - 017"]}
2024-06-20 12:57:36 {"nomorkartu":"0001819523204","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - KDN - 017"]}
2024-06-20 12:57:37 {"nomorkartu":"0002142380463","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - KDN - 017"]}
2024-06-20 12:57:37 {"nomorkartu":"0000119526063","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - KDN - 017"]}
2024-06-20 12:57:37 {"nomorkartu":"0001450488374","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - KDN - 168"]}
2024-06-20 12:57:38 {"nomorkartu":"0001816449827","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - KDN - 017"]}
2024-06-20 12:57:38 {"nomorkartu":"0001467037618","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 005 - 005"],"request":{"noSEP":"0904R0080624V018199","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:39 {"nomorkartu":"0000375269646","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 005 - 005"]}
2024-06-20 12:57:39 {"nomorkartu":"0001233686856","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 005 - 017"]}
2024-06-20 12:57:39 {"nomorkartu":"0001791165341","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 005 - 017"]}
2024-06-20 12:57:40 {"nomorkartu":"0000079968644","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - 005 - 005"],"request":{"noSEP":"0904R0080624V017266","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:40 {"nomorkartu":"0002509734126","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 005 - 005"],"request":{"noSEP":"0904R0080524V023628","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006411","tglRencanaKontrol":"2024-06-20","namaDokter":"Dr H. Agus Waspodo, Sp.PD,KGEH","noKartu":"0002509734126","nama":"PRIBADI TEGUH IP","kelamin":"Laki-laki","tglLahir":"1965-10-21","namaDiagnosa":"E11.8 - Non-insulin-dependent diabetes mellitus with unspecified complications"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080524V023628', '42764', '005', '0904R0080624K006411', '0002509734126', 'Dr H. Agus Waspodo, Sp.PD,KGEH', '2024-06-20', 'PRIBADI TEGUH IP, TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:57:40 {"nomorkartu":"0002878047876","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 005 - 005"],"request":{"noSEP":"0904R0080624V018194","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:41 {"nomorkartu":"0001140958888","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 005 - 017"]}
2024-06-20 12:57:41 {"nomorkartu":"0002315769513","tanggal":"2024-06-20","catatan":["Rujukan RS","13 - 005 - SAR"]}
2024-06-20 12:57:41 {"nomorkartu":"0001225069738","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 005 - 005"],"request":{"noSEP":"0904R0080624V005425","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"200","message":"Ok"},"response":{"noSuratKontrol":"0904R0080624K006412","tglRencanaKontrol":"2024-06-20","namaDokter":"Dr H. Agus Waspodo, Sp.PD,KGEH","noKartu":"0001225069738","nama":"LIM HON TJONG","kelamin":"Laki-laki","tglLahir":"1952-05-08","namaDiagnosa":"C22.0 - Liver cell carcinoma"}},"database":"Error: INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, \n\t\t\t\t\ttglRencanaKontrol, namaPasien, user) VALUES ('0904R0080624V005425', '42764', '005', '0904R0080624K006412', '0001225069738', 'Dr H. Agus Waspodo, Sp.PD,KGEH', '2024-06-20', 'LIM HON TJONG, TN', '1065')<br>MySQL server has gone away"}
2024-06-20 12:57:42 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","5 - 005 - 021"],"nomorkartu":"0002302203677","tanggal":"2024-06-20"}
2024-06-20 12:57:42 {"nomorkartu":"0001093589897","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 005 - 008"]}
2024-06-20 12:57:42 {"nomorkartu":"0002056826564","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 005 - INT"]}
2024-06-20 12:57:42 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","4 - 005 - 017"],"nomorkartu":"0000058851551","tanggal":"2024-06-20"}
2024-06-20 12:57:43 {"nomorkartu":"0000039700991","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 005 - 017"]}
2024-06-20 12:57:43 {"nomorkartu":"0003575987515","tanggal":"2024-06-20","catatan":["Rujukan RS","5 - 005 - 017"]}
2024-06-20 12:57:43 {"nomorkartu":"0000072911529","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 005 - 018"]}
2024-06-20 12:57:43 {"nomorkartu":"0000367186138","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - 005 - URO"]}
2024-06-20 12:57:44 {"nomorkartu":"0001222898207","tanggal":"2024-06-20","catatan":["Rujukan RS","7 - 005 - 005"],"request":{"noSEP":"0904R0080624V012686","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:57:44 {"nomorkartu":"0001613416757","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - 005 - 017"]}
2024-06-20 12:57:45 {"nomorkartu":"0001796723447","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 005 - 017"]}
2024-06-20 12:57:45 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","3 - 005 - INT"],"nomorkartu":"0000814596063","tanggal":"2024-06-20"}
2024-06-20 12:57:45 {"nomorkartu":"0003309985809","tanggal":"2024-06-20","catatan":["Rujukan RS","0 - 005 - 018"]}
2024-06-20 12:57:45 {"nomorkartu":"0001268034298","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - 005 - 008"]}
2024-06-20 12:57:45 {"nomorkartu":"0000048532149","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - 005 - 021"]}
2024-06-20 12:57:46 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","8 - 005 - 005"],"nomorkartu":"0000009530818","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V018087","kodeDokter":"42764","poliKontrol":"005","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:46 {"nomorkartu":"0001324910845","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 005 - 017"]}
2024-06-20 12:57:47 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","1 - 005 - 017"],"nomorkartu":"0001428217378","tanggal":"2024-06-20"}
2024-06-20 12:57:47 {"catatan":["Dokter DPJP di ubah dari 217318 menjadi 42764","Rujukan RS","14 - 005 - 021"],"nomorkartu":"0001829846169","tanggal":"2024-06-20"}
2024-06-20 12:57:47 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - 021"],"nomorkartu":"0000377191765","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016969","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:48 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","4 - 021 - 021"],"nomorkartu":"0000047813466","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016973","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:48 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - OBG"],"nomorkartu":"0000048809046","tanggal":"2024-06-20"}
2024-06-20 12:57:48 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - 021"],"nomorkartu":"0001309726067","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017046","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:49 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - 021"],"nomorkartu":"0001656809842","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016923","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:49 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - 021"],"nomorkartu":"0001306649114","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017176","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:49 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - 018"],"nomorkartu":"0001382118017","tanggal":"2024-06-20"}
2024-06-20 12:57:50 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - OBG"],"nomorkartu":"0001222696967","tanggal":"2024-06-20"}
2024-06-20 12:57:50 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0001262147859","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016943","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:50 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0001771979613","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017841","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:51 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","31 - 021 - SAR"],"nomorkartu":"0001390864612","tanggal":"2024-06-20"}
2024-06-20 12:57:51 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan PCARE"," - 021 - OBG"],"nomorkartu":"0001882646504","tanggal":"2024-06-20"}
2024-06-20 12:57:57 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0001802264501","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017832","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:58 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan PCARE"," - 021 - 021"],"nomorkartu":"0001222708307","tanggal":"2024-06-20"}
2024-06-20 12:57:58 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - 021"],"nomorkartu":"0001030656611","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017962","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:58 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - OBG"],"nomorkartu":"0002690752184","tanggal":"2024-06-20"}
2024-06-20 12:57:59 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","9 - 021 - 021"],"nomorkartu":"0001608616708","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016856","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:57:59 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","18 - 021 - 018"],"nomorkartu":"0002516142137","tanggal":"2024-06-20"}
2024-06-20 12:57:59 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0001648489814","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017292","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:00 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan PCARE"," - 021 - OBG"],"nomorkartu":"0001616461637","tanggal":"2024-06-20"}
2024-06-20 12:58:00 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0002323698028","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017614","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:00 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - 017"],"nomorkartu":"0001339961207","tanggal":"2024-06-20"}
2024-06-20 12:58:01 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","6 - 021 - 021"],"nomorkartu":"0002770743137","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V012817","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:01 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - OBG"],"nomorkartu":"0001389678401","tanggal":"2024-06-20"}
2024-06-20 12:58:01 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - OBG"],"nomorkartu":"0000072557155","tanggal":"2024-06-20"}
2024-06-20 12:58:02 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","0 - 021 - OBG"],"nomorkartu":"0003528814858","tanggal":"2024-06-20"}
2024-06-20 12:58:02 {"catatan":["Dokter DPJP di ubah dari 19405 menjadi 215840","Rujukan RS","3 - 021 - 017"],"nomorkartu":"0001203235402","tanggal":"2024-06-20"}
2024-06-20 12:58:02 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","5 - 021 - 021"],"nomorkartu":"0001303925117","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V016775","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:03 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - OBG"],"nomorkartu":"0001291046872","tanggal":"2024-06-20"}
2024-06-20 12:58:03 {"nomorkartu":"0001725577569","tanggal":"2024-06-20","catatan":["Rujukan RS","8 - 021 - 021"],"request":{"noSEP":"0904R0080624V013949","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:03 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - 017"],"nomorkartu":"0001445624201","tanggal":"2024-06-20"}
2024-06-20 12:58:04 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","5 - 021 - 021"],"nomorkartu":"0002362297735","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017953","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:04 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","4 - 021 - 021"],"nomorkartu":"0001877361783","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017135","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:05 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0001901227779","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V000892","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:05 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan PCARE"," - 021 - KEM"],"nomorkartu":"0002335981487","tanggal":"2024-06-20"}
2024-06-20 12:58:05 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0003144417254","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V018590","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:06 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","5 - 021 - 021"],"nomorkartu":"0002515177787","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017486","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:07 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","5 - 021 - PAR"],"nomorkartu":"0001372674486","tanggal":"2024-06-20"}
2024-06-20 12:58:07 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","6 - 021 - 021"],"nomorkartu":"0002916145642","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V004908","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:07 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","9 - 021 - 021"],"nomorkartu":"0003509251299","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017360","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:08 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","7 - 021 - 021"],"nomorkartu":"0001338523277","tanggal":"2024-06-20","request":{"noSEP":"0904R0080524V025615","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Nomor SEP Sudah pernah digunakan!."},"response":null}}
2024-06-20 12:58:08 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","8 - 021 - 021"],"nomorkartu":"0001258977273","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V004365","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"203","message":"Gagal simpan! Sudah Diterbitkan Rencana Kunjungan Kontrol Di Tanggal Yang Sama."},"response":null}}
2024-06-20 12:58:09 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","5 - 021 - SAR"],"nomorkartu":"0001278211318","tanggal":"2024-06-20"}
2024-06-20 12:58:09 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","1 - 021 - OBG"],"nomorkartu":"0003105895678","tanggal":"2024-06-20"}
2024-06-20 12:58:10 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan PCARE"," - 021 - 017"],"nomorkartu":"0001310156739","tanggal":"2024-06-20"}
2024-06-20 12:58:10 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","3 - 021 - 021"],"nomorkartu":"0001738186514","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017935","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:10 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","8 - 021 - 017"],"nomorkartu":"0001156616662","tanggal":"2024-06-20"}
2024-06-20 12:58:10 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 017"],"nomorkartu":"0002644600329","tanggal":"2024-06-20"}
2024-06-20 12:58:14 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","0 - 021 - 021"],"nomorkartu":"0001305903925","tanggal":"2024-06-20"}
2024-06-20 12:58:14 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","6 - 021 - OBG"],"nomorkartu":"0003005737659","tanggal":"2024-06-20"}
2024-06-20 12:58:14 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0003259493414","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017533","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:15 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","4 - 021 - OBG"],"nomorkartu":"0001143443215","tanggal":"2024-06-20"}
2024-06-20 12:58:15 {"catatan":["Dokter DPJP di ubah dari 217397 menjadi 215840","Rujukan RS","2 - 021 - 021"],"nomorkartu":"0002361516388","tanggal":"2024-06-20","request":{"noSEP":"0904R0080624V017999","kodeDokter":"215840","poliKontrol":"021","tglRencanaKontrol":"2024-06-20","user":"1065"},"response":{"metaData":{"code":"204","message":"Tanggal rencana kontrol tidak boleh lebih kecil dari tanggal SEP"},"response":null}}
2024-06-20 12:58:19 {"nomorkartu":"0001605322991","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - PNM - 008"]}
2024-06-20 12:58:19 {"nomorkartu":"0002444083435","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - PNM - 017"]}
2024-06-20 12:58:19 {"nomorkartu":"0000043252694","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - PNM - 017"]}
2024-06-20 12:58:19 {"nomorkartu":"0000041417392","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - PNM - PAR"]}
2024-06-20 12:58:20 {"nomorkartu":"0001550281015","tanggal":"2024-06-20","catatan":["Rujukan RS","1 - PNM - THT"]}
2024-06-20 12:58:20 {"nomorkartu":"0002316936519","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - PNM - 017"]}
2024-06-20 12:58:20 {"nomorkartu":"0002305202826","tanggal":"2024-06-20","catatan":["Rujukan RS","9 - PNM - THT"]}
2024-06-20 12:58:21 {"nomorkartu":"0001215145438","tanggal":"2024-06-20","catatan":["Rujukan RS","14 - PNM - 008"]}
2024-06-20 12:58:21 {"nomorkartu":"0001740213099","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - PNM - 017"]}
2024-06-20 12:58:21 {"nomorkartu":"0001454268058","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - PNM - 008"]}
2024-06-20 12:58:21 {"nomorkartu":"0001422150478","tanggal":"2024-06-20","catatan":["Rujukan RS","4 - PNM - 017"]}
2024-06-20 12:58:22 {"nomorkartu":"0001097225482","tanggal":"2024-06-20","catatan":["Rujukan RS","2 - PNM - THT"]}
2024-06-20 12:58:22 {"nomorkartu":"0002856145959","tanggal":"2024-06-20","catatan":["Rujukan RS","3 - PNM - 168"]}
2024-06-20 12:58:22 {"nomorkartu":"0001424825932","tanggal":"2024-06-20","catatan":["Rujukan RS","6 - PNM - 008"]}
2024-06-20 12:58:23 {"nomorkartu":"0002334756328","tanggal":"2024-06-20","catatan":["Rujukan PCARE"," - 045 - KEM"]}
2024-06-20 12:58:24 {"nomorkartu":"0000045746548","tanggal":"2024-06-20","catatan":["Rujukan RS","10 - 045 - OBG"]}
